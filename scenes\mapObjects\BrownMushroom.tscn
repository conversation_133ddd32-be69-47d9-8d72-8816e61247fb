[gd_scene load_steps=4 format=3 uid="uid://f0keu4ntu4x0"]

[ext_resource type="Script" uid="uid://c1jqv13waxuiq" path="res://scenes/mapObjects/BrownMushroom.cs" id="1_brownmushroom"]
[ext_resource type="Texture2D" uid="uid://xic4f8u5ejy" path="res://resources/solaria/exterior/Mushrooms4.png" id="2_sunwg"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_brownmushroom"]

[node name="BrownMushroom" type="Node2D"]
script = ExtResource("1_brownmushroom")
MaxHealth = 1

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_sunwg")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="ProgressBar" parent="." instance=ExtResource("3_brownmushroom")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
