using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class Region3Manager : Node2D
{
	[Export] public float BaseSpawnInterval { get; set; } = 15.0f; // Base 15 seconds
	[Export] public int RegionId { get; set; } = 3;
	[Export] public int InitiallySpawnedObjects { get; set; } = 15;
	[Export] public int MaxItemsSpawned { get; set; } = 50;

	// Object spawn probabilities (must add up to 100)
	[Export] public int TreeSpawnChance { get; set; } = 39; // 39% trees (reduced by 1%)
	[Export] public int RockSpawnChance { get; set; } = 24; // 24% rocks (reduced by 1%)
	[Export] public int BerryBushSpawnChance { get; set; } = 20; // 20% berry bushes
	[Export] public int GreenBushSpawnChance { get; set; } = 5; // 5% green bushes
	[Export] public int Rock2SpawnChance { get; set; } = 10; // 10% sandstone rocks
	[Export] public int BrownMushroomSpawnChance { get; set; } = 2; // 2% brown mushrooms

	// Scene references
	[Export] public PackedScene TreeScene { get; set; }
	[Export] public PackedScene RockScene { get; set; }
	[Export] public PackedScene BerryBushScene { get; set; }
	[Export] public PackedScene GreenBushScene { get; set; }
	[Export] public PackedScene Rock2Scene { get; set; }
	[Export] public PackedScene BrownMushroomScene { get; set; }

	// Rabbit spawning configuration
	[Export] public int MaxRabbitsPerRegion { get; set; } = 2;
	[Export] public float RabbitSpawnInterval { get; set; } = 60.0f; // 60 seconds
	[Export] public PackedScene RabbitScene { get; set; }

	private Timer _spawnTimer;
	private Timer _rabbitSpawnTimer;
	private CustomDataLayerManager _customDataManager;
	private Dictionary<Vector2I, Node2D> _activeObjects = new();
	private List<Rabbit> _activeRabbits = new();
	private Dictionary<Vector2I, int> _objectHealthData = new();
	private Random _random = new();
	private bool _isRegionUnlocked = false;

	public override void _Ready()
	{
		CheckRegionUnlockStatus();

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");

		// Always connect to region unlock signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked += OnRegionUnlocked;
		}

		// Always load existing data regardless of unlock status
		CallDeferred(nameof(CleanCorruptedData));
		CallDeferred(nameof(LoadExistingObjects));
		CallDeferred(nameof(LoadExistingRabbits));
		CallDeferred(nameof(LoadObjectHealthData)); // todo remove

		// Always do initial spawn (for visibility when region unlocks)
		CallDeferred(nameof(FirstTimeInitialize));

		// Only start ongoing spawning if region is unlocked
		if (_isRegionUnlocked)
		{
			CallDeferred(nameof(StartOngoingSpawning));
		}
	}

	private void StartOngoingSpawning()
	{
		_spawnTimer = new Timer();
		_spawnTimer.WaitTime = CalculateNextSpawnInterval();
		_spawnTimer.OneShot = true;
		_spawnTimer.Autostart = true;
		_spawnTimer.Timeout += OnSpawnTimer;
		AddChild(_spawnTimer);

		// Setup rabbit spawn timer
		_rabbitSpawnTimer = new Timer();
		_rabbitSpawnTimer.WaitTime = RabbitSpawnInterval;
		_rabbitSpawnTimer.OneShot = true;
		_rabbitSpawnTimer.Autostart = true;
		_rabbitSpawnTimer.Timeout += OnRabbitSpawnTimer;
		AddChild(_rabbitSpawnTimer);
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked -= OnRegionUnlocked;
		}
		base._ExitTree();
	}

	private void CheckRegionUnlockStatus()
	{
		_isRegionUnlocked = GameSaveData.Instance.UnlockedRegions?.Contains(RegionId) ?? false;
	}

	private void OnRegionUnlocked(int regionId)
	{
		if (regionId == RegionId && !_isRegionUnlocked)
		{
			_isRegionUnlocked = true;
			StartOngoingSpawning();
		}
	}

	public void FirstTimeInitialize()
	{
		if(GameSaveData.Instance.FirstTimeInitializedRegions.Contains(RegionId)) return;

		for(var i = 0; i < InitiallySpawnedObjects; i++) TrySpawnObject();
		GameSaveData.Instance.FirstTimeInitializedRegions.Add(RegionId);
	}

	private void OnSpawnTimer()
	{
		TrySpawnObject();

		_spawnTimer.WaitTime = CalculateNextSpawnInterval();
		_spawnTimer.Start();
	}

	private void OnRabbitSpawnTimer()
	{
		TrySpawnRabbit();

		_rabbitSpawnTimer.WaitTime = RabbitSpawnInterval;
		_rabbitSpawnTimer.Start();
	}

	private float CalculateNextSpawnInterval()
	{
		float randomFactor = (float)_random.NextDouble() * 0.4f + 0.8f;
		return BaseSpawnInterval * randomFactor;
	}

	private List<Vector2I> GetValidSpawnPositions()
	{
		var validPositions = new List<Vector2I>();

		var spawnableTiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.CanDestroyableObject &&
			tile.ObjectTypePlaced == ObjectTypePlaced.None);

		foreach (var tile in spawnableTiles)
		{
			if (!_activeObjects.ContainsKey(tile.Position))
			{
				validPositions.Add(tile.Position);
			}
		}

		GD.Print($"Region{RegionId}Manager: Found {validPositions.Count} valid spawn positions in region {RegionId}");
		return validPositions;
	}

	private void TrySpawnObject()
	{
		var validPositions = GetValidSpawnPositions();

		if (validPositions.Count == 0)
		{
			return; // No valid positions available
		}

		var randomIndex = _random.Next(validPositions.Count);
		var spawnPosition = validPositions[randomIndex];

		// Double-check the selected position is in the correct region
		var tileData = _customDataManager.GetTileData(spawnPosition);
		if (tileData.Region != RegionId)
		{
			return;
		}

		var objectType = DetermineObjectTypeToSpawn();

		// if we didnt unlock region 4 - spawn only tree and stone and bush
		if(!GameSaveData.Instance.UnlockedRegions.Contains(4))
		{
			if(!(objectType == ObjectType.Rock || objectType == ObjectType.Tree || objectType == ObjectType.BerryBush))
			{
				return; // Don't spawn this object type, but don't recurse
			}
		}

		SpawnObjectAt(spawnPosition, objectType);
	}

	private ObjectType DetermineObjectTypeToSpawn()
	{
		int randomValue = _random.Next(1, 101);
		int cumulativeChance = 0;

		cumulativeChance += TreeSpawnChance;
		if (randomValue <= cumulativeChance)
			return ObjectType.Tree;

		cumulativeChance += RockSpawnChance;
		if (randomValue <= cumulativeChance)
			return ObjectType.Rock;

		cumulativeChance += BerryBushSpawnChance;
		if (randomValue <= cumulativeChance)
			return ObjectType.BerryBush;

		cumulativeChance += GreenBushSpawnChance;
		if (randomValue <= cumulativeChance)
			return ObjectType.GreenBush;

		cumulativeChance += Rock2SpawnChance;
		if (randomValue <= cumulativeChance)
			return ObjectType.Rock2;

		cumulativeChance += BrownMushroomSpawnChance;
		if (randomValue <= cumulativeChance)
			return ObjectType.BrownMushroom;

		return ObjectType.Tree;
	}

	private void SpawnObjectAt(Vector2I tilePosition, ObjectType objectType)
	{
		// Validate that we're spawning in the correct region
		var tileData = _customDataManager.GetTileData(tilePosition);
		if (tileData.Region != RegionId)
		{
			return;
		}

		Node2D objectInstance = null;

		switch (objectType)
		{
			case ObjectType.Tree:
				objectInstance = SpawnTree(tilePosition);
				break;
			case ObjectType.Rock:
				objectInstance = SpawnRock(tilePosition);
				break;
			case ObjectType.BerryBush:
				objectInstance = SpawnBerryBush(tilePosition);
				break;
			case ObjectType.GreenBush:
				objectInstance = SpawnGreenBush(tilePosition);
				break;
			case ObjectType.Rock2:
				objectInstance = SpawnRock2(tilePosition);
				break;
			case ObjectType.BrownMushroom:
				objectInstance = SpawnBrownMushroom(tilePosition);
				break;
			default:
				return;
		}

		if (objectInstance != null)
		{
			_activeObjects[tilePosition] = objectInstance;

			ObjectTypePlaced placedType = objectType switch
			{
				ObjectType.Tree => ObjectTypePlaced.Tree,
				ObjectType.Rock => ObjectTypePlaced.Rock,
				ObjectType.BerryBush => ObjectTypePlaced.BerryBush,
				ObjectType.GreenBush => ObjectTypePlaced.BerryBush, // Use same placement type as BerryBush
				ObjectType.Rock2 => ObjectTypePlaced.Rock2,
				ObjectType.BrownMushroom => ObjectTypePlaced.BerryBush, // Use same placement type as BerryBush
				_ => ObjectTypePlaced.None
			};
			_customDataManager.SetObjectPlaced(tilePosition, placedType);
		}
	}

	private Tree SpawnTree(Vector2I tilePosition)
	{
		if (TreeScene == null)
		{
			GD.PrintErr("Region3Manager: TreeScene is null!");
			return null;
		}

		var tree = TreeScene.Instantiate<Tree>();
		if (tree == null)
		{
			GD.PrintErr("Region3Manager: Failed to instantiate tree!");
			return null;
		}

		tree.SetTilePosition(tilePosition);

		GetParent().CallDeferred("add_child", tree);

		tree.TreeDestroyed += OnObjectDestroyed;

		RestoreObjectHealth(tilePosition, tree);

		return tree;
	}

	private Rock SpawnRock(Vector2I tilePosition)
	{
		if (RockScene == null)
		{
			return null;
		}

		// Create rock instance
		var rock = RockScene.Instantiate<Rock>();
		if (rock == null)
		{
			return null;
		}

		rock.SetTilePosition(tilePosition);

		GetParent().CallDeferred("add_child", rock);

		rock.RockDestroyed += OnObjectDestroyed;

		RestoreObjectHealth(tilePosition, rock);

		return rock;
	}

	private BerryBush SpawnBerryBush(Vector2I tilePosition)
	{
		if (BerryBushScene == null)
		{
			GD.PrintErr("Region3Manager: BerryBushScene is null!");
			return null;
		}

		var berryBush = BerryBushScene.Instantiate<BerryBush>();
		if (berryBush == null)
		{
			GD.PrintErr("Region3Manager: Failed to instantiate berry bush!");
			return null;
		}

		berryBush.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", berryBush);
		berryBush.BerryBushDestroyed += OnObjectDestroyed;

		RestoreObjectHealth(tilePosition, berryBush);

		return berryBush;
	}

	private GreenBush SpawnGreenBush(Vector2I tilePosition)
	{
		if (GreenBushScene == null)
		{
			GreenBushScene = GD.Load<PackedScene>("res://scenes/mapObjects/GreenBush.tscn");
			if (GreenBushScene == null)
			{
				GD.PrintErr("Region3Manager: Failed to load GreenBush scene!");
				return null;
			}
		}

		var greenBush = GreenBushScene.Instantiate<GreenBush>();
		if (greenBush == null)
		{
			GD.PrintErr("Region3Manager: Failed to instantiate green bush!");
			return null;
		}

		greenBush.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", greenBush);
		greenBush.GreenBushDestroyed += OnObjectDestroyed;

		RestoreObjectHealth(tilePosition, greenBush);

		return greenBush;
	}

	private Rock2 SpawnRock2(Vector2I tilePosition)
	{
		if (Rock2Scene == null)
		{
			GD.PrintErr("Region3Manager: Rock2Scene is null!");
			return null;
		}

		var rock2 = Rock2Scene.Instantiate<Rock2>();
		if (rock2 == null)
		{
			GD.PrintErr("Region3Manager: Failed to instantiate sandstone rock!");
			return null;
		}

		rock2.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", rock2);
		rock2.Rock2Destroyed += OnObjectDestroyed;

		// Restore health if saved data exists
		RestoreObjectHealth(tilePosition, rock2);

		return rock2;
	}

	private BrownMushroom SpawnBrownMushroom(Vector2I tilePosition)
	{
		if (BrownMushroomScene == null)
		{
			BrownMushroomScene = GD.Load<PackedScene>("res://scenes/mapObjects/BrownMushroom.tscn");
			if (BrownMushroomScene == null)
			{
				GD.PrintErr("Region3Manager: Failed to load BrownMushroom scene!");
				return null;
			}
		}

		var brownMushroom = BrownMushroomScene.Instantiate<BrownMushroom>();
		if (brownMushroom == null)
		{
			GD.PrintErr("Region3Manager: Failed to instantiate brown mushroom!");
			return null;
		}

		brownMushroom.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", brownMushroom);
		brownMushroom.BrownMushroomDestroyed += OnObjectDestroyed;

		RestoreObjectHealth(tilePosition, brownMushroom);

		return brownMushroom;
	}

	private void TrySpawnRabbit()
	{
		// Clean up dead rabbits first
		int removedCount = _activeRabbits.RemoveAll(rabbit => !IsInstanceValid(rabbit));
		if (removedCount > 0)
		{
			UpdateRabbitsInGameData();
		}

		// Check if we're under the limit
		if (_activeRabbits.Count >= MaxRabbitsPerRegion)
		{
			GD.Print($"Region{RegionId}Manager: Rabbit limit reached ({_activeRabbits.Count}/{MaxRabbitsPerRegion})");
			return;
		}

		// Find a random valid position for rabbit spawning
		var validPositions = GetValidRabbitSpawnPositions();
		if (validPositions.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No valid rabbit spawn positions available");
			return;
		}

		var randomIndex = _random.Next(validPositions.Count);
		var spawnPosition = validPositions[randomIndex];

		SpawnRabbitAt(spawnPosition);
	}

	private List<Vector2I> GetValidRabbitSpawnPositions()
	{
		var validPositions = new List<Vector2I>();

		// Rabbits can spawn on any grass tile in the region (they don't lock tiles)
		var grassTiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.CanDestroyableObject); // Grass tiles where objects can be placed

		foreach (var tile in grassTiles)
		{
			validPositions.Add(tile.Position);
		}

		GD.Print($"Region{RegionId}Manager: Found {validPositions.Count} valid rabbit spawn positions");
		return validPositions;
	}

	private void SpawnRabbitAt(Vector2I tilePosition)
	{
		if (RabbitScene == null)
		{
			GD.PrintErr("Region3Manager: RabbitScene is null!");
			return;
		}

		var rabbit = RabbitScene.Instantiate<Rabbit>();
		if (rabbit == null)
		{
			GD.PrintErr("Region3Manager: Failed to instantiate rabbit!");
			return;
		}

		// Set rabbit properties
		rabbit.SetRegion(RegionId);
		rabbit.SetTilePosition(tilePosition);

		// Add to scene
		GetParent().CallDeferred("add_child", rabbit);

		// Track the rabbit
		_activeRabbits.Add(rabbit);
		UpdateRabbitsInGameData();

		GD.Print($"Region{RegionId}Manager: Spawned rabbit at {tilePosition} (Total: {_activeRabbits.Count}/{MaxRabbitsPerRegion})");
	}

	private void OnObjectDestroyed(Vector2I tilePosition)
	{
		if (_activeObjects.ContainsKey(tilePosition))
		{
			_activeObjects.Remove(tilePosition);
			UpdateObjectHealthInGameData();
		}

		// Clear tile occupation (object script should handle this, but double-check)
		_customDataManager.ClearObjectPlaced(tilePosition);

		// Save the updated custom layer data immediately
		SaveCustomLayerDataToGameData();
	}

	private void SaveCustomLayerDataToGameData()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null && _customDataManager != null)
		{
			resourcesManager.SaveCustomLayerData(_customDataManager);
		}
	}

	private void CleanCorruptedData()
	{
		GD.Print($"Region{RegionId}Manager: Cleaning corrupted object data...");

		// Find all tiles with invalid coordinates or invalid object types
		var allObjectTiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.ObjectTypePlaced > 0);

		int cleanedCount = 0;

		foreach (var tile in allObjectTiles)
		{
			bool shouldClean = false;

			// Check for invalid coordinates
			if (Math.Abs(tile.Position.X) > 10000 || Math.Abs(tile.Position.Y) > 10000)
			{
				GD.Print($"Region{RegionId}Manager: Cleaning corrupted coordinates at {tile.Position}");
				shouldClean = true;
			}

			// Check for invalid object types
			if (!Enum.IsDefined(typeof(ObjectTypePlaced), tile.ObjectTypePlaced))
			{
				GD.Print($"Region{RegionId}Manager: Cleaning invalid object type {tile.ObjectTypePlaced} at {tile.Position}");
				shouldClean = true;
			}

			// Check for non-resource objects (buildings should not be managed by Region3Manager)
			if (tile.ObjectTypePlaced == ObjectTypePlaced.Building)
			{
				shouldClean = true;
			}

			if (shouldClean)
			{
				_customDataManager.ClearObjectPlaced(tile.Position);
				cleanedCount++;
			}
		}

		if (cleanedCount > 0)
		{
			GD.Print($"Region{RegionId}Manager: Cleaned {cleanedCount} corrupted object entries");

			// Save the cleaned data immediately
			var resourcesManager = ResourcesManager.Instance;
			resourcesManager?.SaveCustomLayerData(_customDataManager);
		}
		else
		{
			GD.Print($"Region{RegionId}Manager: No corrupted data found");
		}
	}

	private void LoadExistingObjects()
	{
		// Clear any existing objects first to prevent conflicts
		_activeObjects.Clear();

		// Get all tiles in this specific region with resource objects placed (exclude buildings)
		var objectTiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.ObjectTypePlaced != ObjectTypePlaced.None &&
			tile.ObjectTypePlaced != ObjectTypePlaced.Building);

		GD.Print($"Region{RegionId}Manager: Loading {objectTiles.Count} existing resource objects for region {RegionId}");

		int loadedCount = 0;
		int skippedCount = 0;

		foreach (var tile in objectTiles)
		{
			// Validate coordinates are reasonable (prevent corrupted save data)
			if (Math.Abs(tile.Position.X) > 10000 || Math.Abs(tile.Position.Y) > 10000)
			{
				GD.PrintErr($"Region3Manager: Skipping object at {tile.Position} - coordinates are invalid/corrupted");
				_customDataManager.ClearObjectPlaced(tile.Position);
				skippedCount++;
				continue;
			}

			// Validate tile before spawning
			if (!tile.CanDestroyableObject)
			{
				GD.PrintErr($"Region3Manager: Skipping object at {tile.Position} - tile doesn't allow destroyable objects");
				// Clear invalid object data
				_customDataManager.ClearObjectPlaced(tile.Position);
				skippedCount++;
				continue;
			}

			// Check if we already have an object here
			if (_activeObjects.ContainsKey(tile.Position))
			{
				GD.PrintErr($"Region3Manager: Skipping object at {tile.Position} - object already exists in _activeObjects");
				skippedCount++;
				continue;
			}

			// ADDITIONAL CHECK: Look for existing object nodes at this position to prevent visual duplicates
			var existingTrees = GetTree().GetNodesInGroup("trees");
			var existingRocks = GetTree().GetNodesInGroup("rocks");
			var existingBushes = GetTree().GetNodesInGroup("bushes");
			bool hasObjectAtPosition = false;

			foreach (Node node in existingTrees)
			{
				if (node is Tree tree && tree.GetTilePosition() == tile.Position)
				{
					hasObjectAtPosition = true;
					break;
				}
			}

			if (!hasObjectAtPosition)
			{
				foreach (Node node in existingRocks)
				{
					if (node is Rock rock && rock.GetTilePosition() == tile.Position)
					{
						hasObjectAtPosition = true;
						break;
					}
					if (node is Rock2 rock2 && rock2.GetTilePosition() == tile.Position)
					{
						hasObjectAtPosition = true;
						break;
					}
				}
			}

			if (!hasObjectAtPosition)
			{
				foreach (Node node in existingBushes)
				{
					if (node is BerryBush berryBush && berryBush.GetTilePosition() == tile.Position)
					{
						hasObjectAtPosition = true;
						break;
					}
				}
			}

			if (hasObjectAtPosition)
			{
				skippedCount++;
				continue;
			}

			// Convert ObjectTypePlaced to ObjectType for spawning
			ObjectType objectType = tile.ObjectTypePlaced switch
			{
				ObjectTypePlaced.Tree => ObjectType.Tree,
				ObjectTypePlaced.Rock => ObjectType.Rock,
				ObjectTypePlaced.BerryBush => ObjectType.BerryBush,
				ObjectTypePlaced.Rock2 => ObjectType.Rock2,
				ObjectTypePlaced.Building => ObjectType.Anvil, // Default building type
				_ => ObjectType.Empty
			};

			// Skip invalid or empty types
			if (objectType == ObjectType.Empty)
			{
				skippedCount++;
				continue;
			}

			// Skip buildings - they should be handled by BuildingManager
			if (objectType.IsBuilding())
			{
				skippedCount++;
				continue;
			}

			GD.Print($"Region{RegionId}Manager: Loading {objectType} from save at {tile.Position} (Region: {tile.Region})");
			SpawnObjectAt(tile.Position, objectType);
			loadedCount++;
		}

		GD.Print($"Region{RegionId}Manager: Loaded {loadedCount} resource objects, skipped {skippedCount} invalid objects");
	}

	private void LoadExistingRabbits()
	{
		_activeRabbits.Clear();

		// Load rabbit save data from ResourcesManager
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		var rabbitSaveDataList = resourcesManager.LoadRabbitData(RegionId);
		if (rabbitSaveDataList == null || rabbitSaveDataList.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No saved rabbits found for region {RegionId}");
			return;
		}

		GD.Print($"Region{RegionId}Manager: Loading {rabbitSaveDataList.Count} saved rabbits");

		foreach (var saveData in rabbitSaveDataList)
		{
			if (RabbitScene == null) continue;

			var rabbit = RabbitScene.Instantiate<Rabbit>();
			if (rabbit == null) continue;

			// Load rabbit from save data
			rabbit.LoadFromSaveData(saveData);

			// Add to scene and track
			GetParent().CallDeferred("add_child", rabbit);
			_activeRabbits.Add(rabbit);
		}

		GD.Print($"Region{RegionId}Manager: Loaded {_activeRabbits.Count} rabbits for region {RegionId}");
	}

	private void UpdateRabbitsInGameData()
	{
		// Clean up dead rabbits first
		_activeRabbits.RemoveAll(rabbit => !IsInstanceValid(rabbit));

		var rabbitSaveDataList = new List<RabbitSaveData>();

		foreach (var rabbit in _activeRabbits)
		{
			if (rabbit.GetRegion() == RegionId)
			{
				rabbitSaveDataList.Add(rabbit.GetSaveData());
			}
		}

		// Update GameData directly
		string key = $"rabbits_region_{RegionId}";
		GameSaveData.Instance.WorldData.CustomLayerData[key] = rabbitSaveDataList;

		GD.Print($"Region{RegionId}Manager: Updated {rabbitSaveDataList.Count} rabbits for region {RegionId} in GameData");
	}

	private void LoadObjectHealthData()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		_objectHealthData = resourcesManager.LoadObjectHealthData(RegionId);
		GD.Print($"Region{RegionId}Manager: Loaded health data for {_objectHealthData.Count} objects");
	}

	private void UpdateObjectHealthInGameData()
	{
		// Collect current health from all active objects
		_objectHealthData.Clear();

		foreach (var kvp in _activeObjects)
		{
			var position = kvp.Key;
			var obj = kvp.Value;

			if (obj is IDestroyableObject destroyableObj)
			{
				_objectHealthData[position] = destroyableObj.GetCurrentHealth();
			}
		}

		// Update GameData directly
		string key = $"object_health_region_{RegionId}";
		GameSaveData.Instance.WorldData.CustomLayerData[key] = _objectHealthData;

		GD.Print($"Region{RegionId}Manager: Updated health data for {_objectHealthData.Count} objects in GameData");
	}

	private void RestoreObjectHealth(Vector2I position, IDestroyableObject obj)
	{
		if (_objectHealthData.TryGetValue(position, out int savedHealth))
		{
			obj.SetCurrentHealth(savedHealth);
			GD.Print($"Region{RegionId}Manager: Restored health {savedHealth} for object at {position}");
		}
	}
}
