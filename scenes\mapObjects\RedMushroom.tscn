[gd_scene load_steps=4 format=3 uid="uid://ct1timrybdh8t"]

[ext_resource type="Script" uid="uid://cupxrpvwcuc04" path="res://scenes/mapObjects/RedMushroom.cs" id="1_redmushroom"]
[ext_resource type="Texture2D" uid="uid://bbccd166xcp07" path="res://resources/solaria/exterior/Mushrooms3.png" id="2_yy6aw"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_redmushroom"]

[node name="RedMushroom" type="Node2D"]
script = ExtResource("1_redmushroom")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_yy6aw")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="ProgressBar" parent="." instance=ExtResource("3_redmushroom")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
