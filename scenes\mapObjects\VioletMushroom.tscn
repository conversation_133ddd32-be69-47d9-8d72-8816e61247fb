[gd_scene load_steps=4 format=3 uid="uid://dspi34vxf5vqe"]

[ext_resource type="Script" uid="uid://dvrsaoouy8pap" path="res://scenes/mapObjects/VioletMushroom.cs" id="1_violetmushroom"]
[ext_resource type="Texture2D" uid="uid://b5y7ifgh1krvp" path="res://resources/solaria/exterior/Mushrooms2.png" id="2_qtoat"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_violetmushroom"]

[node name="VioletMushroom" type="Node2D"]
script = ExtResource("1_violetmushroom")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_qtoat")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="ProgressBar" parent="." instance=ExtResource("3_violetmushroom")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
