using Godot;

public partial class MapPanel : CanvasLayer
{
	private Button _closeButton;

	public override void _Ready()
	{
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");

		if (_closeButton != null)
		{
			_closeButton.Pressed += OnCloseButtonPressed;
		}
		
		GetNode<Sprite2D>("Control/Panel").Visible = false;
	}

	public override void _ExitTree()
	{
		if (_closeButton != null)
		{
			_closeButton.Pressed -= OnCloseButtonPressed;
		}
	}

	public void OpenPanel()
	{
		GetNode<AnimationPlayer>("AnimationPlayer").Play("Open");
		GD.Print("MapPanel: Panel opened");
	}

	public void ClosePanel()
	{
		GetNode<AnimationPlayer>("AnimationPlayer").Play("Close");
		GD.Print("MapPanel: Panel closed");
	}

	private void OnCloseButtonPressed()
	{
		ClosePanel();
	}
}
