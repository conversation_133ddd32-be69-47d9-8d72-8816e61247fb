[gd_scene load_steps=19 format=3 uid="uid://dd328ucgp21ei"]

[ext_resource type="Texture2D" uid="uid://d1xd3q7omqhh5" path="res://resources/Fantasy Dreamland/Characters Pack 1/Character_023.png" id="1_qh0t2"]
[ext_resource type="Script" uid="uid://ch4ta7gk3yofh" path="res://scenes/npcs/TutorialNPC.cs" id="1_script"]
[ext_resource type="Texture2D" uid="uid://c6pcs5p57mb0m" path="res://resources/solaria/UI/dialogs/dialog1.png" id="2_qhmda"]
[ext_resource type="Texture2D" uid="uid://coohtgqediyob" path="res://resources/solaria/UI/dialogs/dialogGreenArrow.png" id="3_806w0"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="4_806w0"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_5evfb"]
size = Vector2(8, 4)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1mxkk"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gnnrd"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_bj88v"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_papxs"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_0vg6t"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_10txw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nhcfw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_whogm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_txsbh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_emg2x"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_vbhon"]

[sub_resource type="CircleShape2D" id="CircleShape2D_806w0"]
radius = 13.0

[node name="TutorialNpc" type="Sprite2D"]
y_sort_enabled = true
texture = ExtResource("1_qh0t2")
hframes = 4
vframes = 4
frame = 3
script = ExtResource("1_script")

[node name="StaticBody2d" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2d"]
y_sort_enabled = true
position = Vector2(0, 10)
shape = SubResource("RectangleShape2D_5evfb")

[node name="Dialog" type="Node2D" parent="."]
z_index = 1

[node name="DialogPanel" type="Sprite2D" parent="Dialog"]
position = Vector2(-34, -20)
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_qhmda")

[node name="ContinueMark" type="Sprite2D" parent="Dialog"]
position = Vector2(-34, -13)
scale = Vector2(0.5, 0.5)
texture = ExtResource("3_806w0")

[node name="Label" parent="Dialog" instance=ExtResource("4_806w0")]
offset_left = -74.0
offset_top = -40.0
offset_right = 121.0
offset_bottom = 21.0
scale = Vector2(0.41, 0.41)
text = "Hello friend... this is a test message to see how the text will be distributed. "

[node name="DialogContinueButton" type="Button" parent="Dialog"]
offset_left = -74.0
offset_top = -40.0
offset_right = 6.0
offset_bottom = -8.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_1mxkk")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_gnnrd")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_bj88v")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_papxs")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_0vg6t")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_10txw")
theme_override_styles/hover = SubResource("StyleBoxEmpty_nhcfw")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_whogm")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_txsbh")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_emg2x")
theme_override_styles/normal = SubResource("StyleBoxEmpty_vbhon")

[node name="PlayerDetectionArea2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetectionArea2D"]
position = Vector2(0, 3)
shape = SubResource("CircleShape2D_806w0")
