using Godot;
using System;

/// <summary>
/// BrownMushroom object that can be destroyed by pickaxe
/// Handles hit animations, health management, and resource dropping
/// Drops BrownMushroom resources when destroyed
/// </summary>
public partial class BrownMushroom : Node2D, IDestroyableObject
{
	[Export] public int MaxHealth { get; set; } = 2;

	// Event for when brown mushroom is destroyed
	[Signal] public delegate void BrownMushroomDestroyedEventHandler(Vector2I tilePosition);

	private int _currentHealth;
	private Sprite2D _sprite;
	private Tween _hitTween;
	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;

	public override void _Ready()
	{
		_currentHealth = MaxHealth;
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_hpBar = GetNode<ProgressBar>("ProgressBar");

		// Get CustomDataLayerManager from the world scene
		var world = GetNode("/root/world");
		if (world != null)
		{
			_customDataManager = world.GetNode<CustomDataLayerManager>("CustomDataLayerManager");
		}

		if (_customDataManager == null)
		{
			GD.PrintErr("BrownMushroom: CustomDataLayerManager not found!");
		}

		// Only calculate tile position if it hasn't been set by spawner
		if (_tilePosition == Vector2I.Zero)
		{
			// Calculate tile position from world position (for manually placed mushrooms)
			_tilePosition = new Vector2I((int)(GlobalPosition.X - 8) / 16, (int)(GlobalPosition.Y - 8) / 16);

			// Mark tile as occupied (only for manually placed mushrooms)
			_customDataManager?.SetObjectPlaced(_tilePosition, ObjectTypePlaced.BerryBush);
		}

		// Initialize HP bar
		UpdateHPBar();

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}
	}

	/// <summary>
	/// Take damage from pickaxe
	/// </summary>
	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;

		// Update HP bar
		UpdateHPBar();

		// Play hit animation
		PlayHitAnimation();

		// Check if brown mushroom should be destroyed
		if (_currentHealth <= 0)
		{
			DestroyBrownMushroom();
		}
	}

	/// <summary>
	/// Play hit animation with color modulation
	/// </summary>
	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		// Stop any existing tween
		_hitTween?.Kill();

		// Create new tween for hit animation
		_hitTween = CreateTween();

		// Modulate to hit color (reddish)
		_hitTween.TweenProperty(_sprite, "modulate", new Color(1.0f, 0.4f, 0.4f, 1.0f), 0.1f);

		// Return to normal color
		_hitTween.TweenProperty(_sprite, "modulate", Colors.White, 0.1f);
	}

	/// <summary>
	/// Destroy the brown mushroom and drop resources
	/// </summary>
	private void DestroyBrownMushroom()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		// Drop resources
		DropResources();

		// Clear tile occupation
		_customDataManager?.ClearObjectPlaced(_tilePosition);

		// Give XP reward
		CommonSignals.Instance?.EmitAddXp(2);

		// Emit destruction signal
		EmitSignal(SignalName.BrownMushroomDestroyed, _tilePosition);

		// Remove from scene
		QueueFree();
	}

	/// <summary>
	/// Drop resources when brown mushroom is destroyed (1 BrownMushroom + 1 Leaf)
	/// </summary>
	private void DropResources()
	{
		// Drop 1 BrownMushroom
		Vector2 mushroomOffset = new Vector2(
			(float)(GD.Randf() - 0.5f) * 8.0f, // Random X offset: -4 to +4 pixels
			(float)(GD.Randf() - 0.5f) * 8.0f  // Random Y offset: -4 to +4 pixels
		);
		Vector2 mushroomSpawnPosition = GlobalPosition + mushroomOffset;
		DroppedResource.SpawnResource(mushroomSpawnPosition, ResourceType.BrownMushroom, 1);

		// Drop 1 Leaf
		Vector2 leafOffset = new Vector2(
			(float)(GD.Randf() - 0.5f) * 8.0f, // Random X offset: -4 to +4 pixels
			(float)(GD.Randf() - 0.5f) * 8.0f  // Random Y offset: -4 to +4 pixels
		);
		Vector2 leafSpawnPosition = GlobalPosition + leafOffset;
		DroppedResource.SpawnResource(leafSpawnPosition, ResourceType.Leaf, 1);
	}

	/// <summary>
	/// Get current health
	/// </summary>
	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	/// <summary>
	/// Set current health
	/// </summary>
	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	/// <summary>
	/// Get tile position
	/// </summary>
	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	/// <summary>
	/// Set tile position (used by spawners)
	/// </summary>
	public void SetTilePosition(Vector2I position)
	{
		_tilePosition = position;
	}

	/// <summary>
	/// Update HP bar based on current health
	/// </summary>
	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		// Calculate health percentage
		float healthPercentage = (float)_currentHealth / MaxHealth;

		// If at full health, hide HP bar
		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Hide();
		}
		else
		{
			// Show HP bar and set progress
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (_tilePosition == tilePosition)
		{
			Vector2I playerTile = GetPlayerTilePosition();
			if (CanBeHitFrom(playerTile))
			{
				TakeDamage(damage);
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			Vector2 playerPos = player.GlobalPosition;
			return new Vector2I((int)playerPos.X / 16, (int)playerPos.Y / 16);
		}
		return Vector2I.Zero;
	}

	public bool CanBeHitFrom(Vector2I playerTile)
	{
		Vector2I distance = _tilePosition - playerTile;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}
}
