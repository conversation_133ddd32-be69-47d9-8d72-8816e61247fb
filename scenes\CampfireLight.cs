using Godot;

public partial class CampfireLight : Node2D
{
	[Export] public float LightRadius { get; set; } = 96.0f;
	[Export] public Color LightColor { get; set; } = new Color(1.0f, 0.8f, 0.6f, 1.0f); // Warm orange
	[Export] public float LightEnergy { get; set; } = 1.2f;
	[Export] public bool OnlyShowAtNight { get; set; } = true;
	[Export] public float FlickerIntensity { get; set; } = 0.1f;
	[Export] public float FlickerSpeed { get; set; } = 2.0f;

	private Light2D _light;
	private DayNightManager _dayNightManager;
	private float _baseEnergy;
	private float _flickerTime = 0.0f;

	public override void _Ready()
	{
		// Get the Light2D node from the scene
		_light = GetParent().GetNode<Light2D>("CampfireLight");
		if (_light == null)
		{
			GD.PrintErr("CampfireLight: Light2D node not found in scene!");
			return;
		}

		_light.Enabled = true;
		_light.Energy = LightEnergy;
		_light.Color = LightColor;
		_light.Scale = Vector2.One * (LightRadius / 64.0f); // Scale the light

		_baseEnergy = LightEnergy;

		// Find the DayNightManager
		_dayNightManager = GetNode<DayNightManager>("/root/world/DayNight");
		if (_dayNightManager == null)
		{
			// Try alternative path
			_dayNightManager = GetTree().GetFirstNodeInGroup("DayNightManager") as DayNightManager;
			if (_dayNightManager == null)
			{
				GD.PrintErr("CampfireLight: DayNightManager not found!");
			}
		}

		GD.Print($"CampfireLight: Initialized with radius {LightRadius}");
	}

	public override void _Process(double delta)
	{
		if (_light == null || _dayNightManager == null) return;

		// Show/hide light based on day/night cycle
		if (OnlyShowAtNight)
		{
			_light.Visible = _dayNightManager.IsNight();
		}
		else
		{
			_light.Visible = true;
		}

		// Add flickering effect for campfire
		if (_light.Visible)
		{
			_flickerTime += (float)delta * FlickerSpeed;
			float flicker = Mathf.Sin(_flickerTime) * FlickerIntensity;
			_light.Energy = _baseEnergy + flicker;
		}
	}

	public void SetLightRadius(float radius)
	{
		LightRadius = radius;
		if (_light != null)
		{
			_light.Scale = Vector2.One * (radius / 64.0f);
		}
		GD.Print($"CampfireLight: Radius set to {radius}");
	}

	public void SetLightColor(Color color)
	{
		LightColor = color;
		if (_light != null)
		{
			_light.Color = color;
		}
	}

	public void SetLightEnergy(float energy)
	{
		LightEnergy = energy;
		_baseEnergy = energy;
		if (_light != null)
		{
			_light.Energy = energy;
		}
	}

	public void SetOnlyShowAtNight(bool nightOnly)
	{
		OnlyShowAtNight = nightOnly;
		GD.Print($"CampfireLight: OnlyShowAtNight set to {nightOnly}");
	}

	public void SetFlickerIntensity(float intensity)
	{
		FlickerIntensity = intensity;
		GD.Print($"CampfireLight: Flicker intensity set to {intensity}");
	}

	public void SetFlickerSpeed(float speed)
	{
		FlickerSpeed = speed;
		GD.Print($"CampfireLight: Flicker speed set to {speed}");
	}
}
