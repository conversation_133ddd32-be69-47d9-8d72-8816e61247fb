1. When i select something in inventory (like berries) and use them then amount not refreshing, only after reopening inventory - fix
2. In the quick action (SelectedToolPanel) when i click multiple keys at the same time (for example 1,2,3) then it selects 3 items - it shows this placeholder for selected item on all 3 items - fix
3. Add translations for:
* PRODUCE_TEXT - Produce
* FURNACE2_TEXT - Furnace 2
* FURNACE2_DESCRIPTION - Allows smelting of Gold and Indigosium ores into bars.
* FURNACE3_TEXT - Furnace 3
* FURNACE3_DESCRIPTION - Allows smelting of Mithril and Erithrydium ores into bars.
* FURNACE4_TEXT - Furnace 4
* FURNACE4_DESCRIPTION - Allows smelting of Adamantite and Uranium ores into bars.
4. In campfire/furnace menus when i select something and it is displayed in infoboard - then defauld amount of selected item is 1. Change it to 0.
5. When i selected campfire/furnace/anvil and in given list item i dont have enough resources then i want the BuildButton to be grayed out just like in BuildMenu. Verify where it is grayed out and where not and adjust.
6. When player has no energy then he should not be able to use tools. Currently he can use tools even when he has 0 energy.