# Crafterio - Technical Design Document

## Game Overview
Resource gathering and crafting game inspired by Forager, developed in Godot 4.4 with C#. Player gathers resources, builds structures, fights enemies, and unlocks new regions.

## Core Architecture

### Map Layers & Custom Data System
**Multi-layer TileMap with custom properties for complex gameplay mechanics:**

**Layer Structure:**
- **Floor/Environment**: Terrain, paths, bridges, decorative elements
- **Blockers**: Walls, barriers, impassable terrain
- **Custom Data Layers**: Multiple TileMapLayer instances with gameplay properties

**CustomDataLayerManager**: Unified system for tile data management
- JSON serialization for all custom data fields
- Get/set methods across multiple layers
- Automatic save/load integration with ResourcesManager

**Custom Properties:**
- `speedModifier` (float): Movement speed multiplier (1.0 = normal, >1.0 = faster)
- `canBuilding/canDestroyableObject/canPlant/canEnemy/canBridge/canFishing` (bool): Tile permissions
- `objectTypePlaced` (int): Object type enum for tile occupancy tracking
- `region` (int): Region assignment for object spawning

### Regions
4 distinct island regions connected by player-built bridges. Each region has unique resource spawns, enemy types, and visual themes.

### Bridge Building System
**Bridge Construction**: Players can build bridges to cross water tiles
- **Cost**: 5 wooden planks + 2 wooden beams
- **Placement**: 1x1 tile on water tiles with `CanBridge = true` property
- **Health System**: 8 HP, shows health bar when damaged, can be destroyed with pickaxe
- **Enhanced Hit Animation**: Color flash and scale animation similar to anvil system
- **Tile Management**: Saves original water tile data, restores when bridge destroyed
- **Layer**: Places bridge tiles on `Layer2Floor_Bridge_SpeedModifier` tilemap layer
- **Save/Load**: Bridge data persists between game sessions via BuildingManager

### Multi-Furnace Smelting System
**Complete Ore Processing Chain**: Four-tier furnace system for all metal bar production
- **Furnace1 (2x2)**: Copper/Iron bar production requiring 10 copper ore + 3 iron ore
- **Furnace2 (2x2)**: Gold/Indigosium bar production requiring 10 gold ore + 3 indigosium ore
- **Furnace3 (2x2)**: Mithril/Erithrydium bar production requiring 10 mithril ore + 3 erithrydium ore
- **Furnace4 (2x2)**: Adamantite/Uranium bar production requiring 10 adamantite ore + 3 uranium ore
- **Unified Recipe System**: All furnaces convert 3 ore + 1 wood → 1 bar (3 second process)
- **Interactive Menus**: Dedicated menu system for each furnace with recipe selection
- **Multi-Tile Placement**: All furnaces occupy 2x2 tile area with center positioning
- **Health System**: 25 HP for all furnaces, enhanced durability for advanced buildings
- **Progress Tracking**: Visual smelting progress with timer system for all furnaces
- **Build Menu Integration**: All four furnaces integrated with affordability checking

### Grindstone Processing System
**Wood Processing Building**: Dedicated building for wood item crafting
- **Grindstone (2x2)**: Processes wood items requiring 5 wooden beams + 10 stone to build
- **Wood Processing**: Converts 2 wood → 1 plank (8 second process)
- **Plank Processing**: Converts 3 planks → 1 wooden beam (12 second process)
- **4-Frame Animation**: Working animation with 4 sprite frames for visual feedback
- **Interactive Menu**: Dedicated GrindstoneMenu with resource selection and amount controls
- **Multi-Tile Placement**: Occupies 2x2 tile area with center positioning like furnaces
- **Health System**: 25 HP with repair functionality using hammer tool
- **Progress Tracking**: Visual grinding progress with timer system and resource display
- **Build Menu Integration**: Integrated with affordability checking and placement system

### Food Consumption System
**Enhanced Food Mechanics**: Timeout system for balanced consumption
- **Rabbit Leg Timeout**: 0.5 second cooldown for both raw and cooked rabbit legs
- **Berry Timeout**: Reduced from 3.0s to 0.5s for consistent food timing
- **Unified Timeout Logic**: Consistent timing across all consumable food items
- **Spam Prevention**: Prevents rapid consumption while maintaining responsive gameplay

### Region Blocker System
**Region Access Control**: Manages access to different game regions
- **RegionBlocker.cs**: Controls visibility and access to regions based on unlock status
- **Save Integration**: Region unlock status saved in GameSaveData.UnlockedRegions
- **Visual Feedback**: Fade-out animation when region is unlocked (2 second tween)
- **Signal System**: Uses CommonSignals.RegionUnlocked for cross-system communication
- **Default State**: Region 1 unlocked by default, others require progression

### Map & Region Unlock System
**Interactive Map Interface**: Complete map system for region management and unlocking
- **MapButton**: Opens map panel from SelectedToolPanel UI
- **MapPanel**: Full-screen map interface with region unlock buttons and close functionality
- **RegionUnlockButton**: Individual region unlock controls with gold-based purchasing
- **Sequential Unlocking**: Regions must be unlocked in order (can't unlock region 5 without region 4)
- **Gold-Based Unlocking**: Players can purchase region access using accumulated gold
- **Automatic Unlocking**: Regions 2-3 unlock through tutorial progression (bridge building, cooking)
- **Manual Unlocking**: Regions 4+ require gold purchase through map interface
- **Visual States**: Buttons show unlocked/locked states with price display
- **Save Integration**: All unlock states persist through game save/load system

## Gameplay Systems

### Resource Gathering & Destroyable Objects
**IDestroyableObject Interface**: Extensible system for harvestable objects
- **Interface Methods**: `TakeDamage()`, `CanBeHitFrom()`, `GetTilePosition()`
- **Implemented Objects**: Trees (8 HP, 3 Wood), Rocks (12 HP, 2 Stone)
- **Interaction**: Pickaxe tool with distance validation and hit animations
- **Visual Effects**: White tint + scale animation on hit, transparency when player behind

**Resource Dropping System**: Animated resource collection
- **Drop Animation**: Arc movement (up then down) within 8px radius, 0.75s duration
- **Collection**: Auto-collect when player within 8px range, smooth movement to player
- **Persistence**: Dropped resources saved/loaded with game state
- **TextureManager**: Dual texture system (regular + icon textures for dropped items)

### Tool Animation & Selection System
**SelectedToolPanel UI**: 10-slot quick select system with keyboard (1-0) and mouse input
- **Tool Selection**: Managed by SelectedToolPanel, no longer in PlayerController
- **Tool Usage**: Press 'E' key to use currently selected tool
- **Animation System**: Tools play character animations, sprites managed by AnimationPlayer
- **Tool Sprites**: Only visible during usage, hidden otherwise
- **Unassign System**: UnassignSprite and UnassignButton for each slot
- **Inventory Integration**: Unassign elements visible only when inventory menu is open
- **Slot Management**: Click unassign button to clear slot using ResourcesManager.ClearQuickSelectSlot()

**7 Available Tools** with directional animations (up/down/left/right):
- **Pickaxe**: 10 frames, hits destroyable objects (trees, rocks), damage = level + 2
- **Sword**: 9 frames, placeholder action
- **Bow**: 6 frames, shoots arrows with mouse aiming and 180° restriction
- **Hammer**: 20 frames (2.0s duration), repairs buildings and crafts items, power = level + 2
- **Hoe**: 18 frames (1.8s duration), placeholder action
- **Watering Can**: 5 frames (0.75s duration), placeholder action

**Resource Usage System**: Resources (like Net) use instant actions without animations
- **Net**: Classified as ResourceType.Net, instant usage with placeholder action
- **Future Resources**: Wood, Stone placement for building/crafting

### Inventory & Item Management System
**InventoryMenu**: Multi-sector inventory system with item selection and description
- **Open/Close**: 'Q' key opens, close button only closes
- **Multi-Sector Display**: 2 sectors (8 slots), expandable based on MaxInventorySlots
- **Item Selection**: Single selection across all sectors with animated placeholders
- **Real-Time Updates**: Refreshes when resources change

**InventoryItemDescriptionPanel**: Interactive item information and actions
- **Item Display**: Shows selected item icon, title, and description
- **Description Variants**: TextOnly type with expandable enum system
- **4 Action Buttons**: Use (474747/gray), Remove, Quick Action, Button4 (reserved)
- **Smart Button States**: Enabled/disabled based on item capabilities

**ItemInformation Static Class**: Centralized item data and behavior flags
- **Resource Info**: Wood, Stone, Net with titles, descriptions, usage flags
- **Tool Info**: All 7 tools with descriptions and assignment capabilities
- **Behavior Flags**: CanBeUsed, CanAssignToQuickUse per item type
- **Description Types**: TextOnly enum with future expansion support

**CommonSignals System**: Centralized event management for item interactions
- **UseResourceRequested**: Signal for resource usage attempts
- **ResourceUsed**: Signal for successful resource consumption with quantity updates
- **Auto-Deselection**: Clears selection when item quantity reaches 0
- **Cross-System Communication**: Enables loose coupling between UI components

### Combat System
- **Bow**: Mouse-aimed shooting with 180° restriction, arrow projectiles, direction indicator UI
- **Sword**: Melee combat with animation lock and damage timing
- **Future**: Magic stick (AoE), enemy AI with pathfinding

### Building & Crafting System
**BuildingPlacer & BuildingManager**: Complete building construction system
- **Mouse Placement**: Click to place buildings with real-time validation
- **Tile Validation**: Uses `canBuilding` and `objectTypePlaced` custom data layers
- **Visual Feedback**: Red tint for invalid placement, normal for valid
- **Multi-Tile Support**: Buildings can occupy multiple tiles (e.g., 2x1 anvils)
- **Save/Load Integration**: Buildings persist with position and health data

**Anvil Crafting System**: Instance-based crafting with resource transformation
- **Per-Anvil Menus**: Each anvil has its own AnvilMenu for independent crafting
- **Multiple Recipes**:
  - Wooden Plank: 2 wood + 2 stone → 1 plank (6 hammer hits)
  - Wooden Beam: 2 planks + 2 stone → 1 beam (8 hammer hits)
  - Wooden Stick: 1 wood → 1 stick (3 hammer hits)
- **Crafting Flow**: Select recipe → Hammer anvil → Resource drops on completion
- **Persistent Selection**: Recipe stays selected until resources insufficient
- **Auto-Deselection**: Automatically deselects when player can't afford another craft
- **Resource Dropping**: Completed items drop as DroppedResource (like tree/rock destruction)
- **Progress Tracking**: Vertical progress bar shows crafting completion (0-100%)

**Building Health & Repair System**:
- **Health Management**: Buildings have MaxHealth and current health tracking
- **Damage Visualization**: Hit animations with white tint and scale effects
- **Repair Mechanics**: Hammer tool repairs buildings using hammer power
- **Health Progress Bars**: Horizontal bars show building health, hidden when full
- **Destruction**: Buildings can be destroyed and cleaned up from tile data

**Campfire Cooking System**: Food preparation and cooking mechanics
- **Building Cost**: 5 wood + 2 stone to construct
- **Multi-Tile Placement**: 1x2 vertical tile occupation for realistic size
- **Cooking Recipes**: Raw Rabbit Leg + Wood → Cooked Rabbit Leg (10 seconds)
- **Interactive Menu**: CampfireMenu.cs with recipe selection and quantity controls
- **Progress Tracking**: Visual cooking progress with timer system
- **Resource Management**: Automatic resource consumption and cooked item spawning
- **Animation**: Campfire animation plays continuously when placed
- **Tutorial Integration**: Signals tutorial NPC when campfire is built

**Multi-Furnace Smelting System**: Complete ore processing chain for all metal bars
- **Furnace1**: 10 copper ore + 3 iron ore → Copper/Iron bars (3 ore + 1 wood → 1 bar)
- **Furnace2**: 10 gold ore + 3 indigosium ore → Gold/Indigosium bars (3 ore + 1 wood → 1 bar)
- **Furnace3**: 10 mithril ore + 3 erithrydium ore → Mithril/Erithrydium bars (3 ore + 1 wood → 1 bar)
- **Furnace4**: 10 adamantite ore + 3 uranium ore → Adamantite/Uranium bars (3 ore + 1 wood → 1 bar)
- **Multi-Tile Placement**: All furnaces occupy 2x2 tile area for large industrial buildings
- **Interactive Menus**: Dedicated menu system for each furnace with recipe selection
- **Progress Tracking**: Visual smelting progress with timer system for all furnaces
- **Resource Management**: Automatic resource consumption and bar production
- **Enhanced Durability**: 25 HP for all furnaces, advanced building resilience

### Player Progression
- Health/hunger systems, tool upgrades, skill points
- Death system: respawn at start, load from previous day
- Currency/resources unlock new regions

### Tutorial & Quest System
**TutorialNPC**: Guided learning system for new players
- **Quest Progression**: 7-stage tutorial covering all basic mechanics
  1. Welcome - Introduction to the game
  2. Build Anvil - Learn building system (5 wood + 5 stone)
  3. Build Bridge - Learn advanced building (5 planks + 2 beams)
  4. Hunt Rabbit - Learn combat and hunting mechanics
  5. Build Campfire - Learn cooking infrastructure (5 wood + 2 stone)
  6. Cook Meat - Learn food preparation system
  7. Complete - Tutorial finished
- **Signal Integration**: Listens to building and hunting signals for automatic progression
- **Dialog System**: Multi-line conversations with typewriter effect (0.1s per character)
- **Typewriter Animation**: Character-by-character text display with skip functionality
- **Save Persistence**: Quest progress saved in GameSaveData.TutorialQuestState
- **Interactive UI**: Press 'E' to talk, player detection area for proximity
- **Completion Rewards**: NPC disappears and spawns reward chest with gold and WoodenKey
- **Localized Content**: All dialog text supports multi-language system

### Chest & Treasure System
**Interactive Chest System**: Treasure containers with key-based unlocking
- **Chest Types**: WoodenChest, StoneChest with different unlock requirements
- **Key System**: WoodenKey required to open WoodenChest, consumed on use
- **Gold Rewards**: Configurable gold amounts spawned as collectible items
- **Resource Rewards**: Configurable list of resources with quantities
- **Animation Integration**: OpenChest animation plays before reward spawning
- **Player Interaction**: Press 'R' when near chest to attempt opening
- **Resource Spawning**: Follows same pattern as Rock destruction for consistency
- **Save Persistence**: Chest data saved with position, contents, and opened state
- **Tutorial Integration**: Reward chest spawned when tutorial quest completes

### Quest Board System
**Interactive Quest Management**: Complete quest system with board interaction and reward distribution
- **Quest Board Interaction**: Press 'R' when near quest board to open quest menu
- **Player Detection**: Area2D collision system detects player proximity (layer 3 PlayerDetector)
- **Static Quest Data**: Predefined quests with ID, header, description, requirements, and rewards
- **Quest Requirements**: Resource-based requirements (Wood, Stone, Plank, etc.) with quantities
- **Dual Reward System**: Gold rewards (added to player money) and XP rewards (through signal system)
- **Quest Activation**: QuestData tracks activated and completed quests for save persistence
- **Dynamic UI**: Dark/Light quest item templates with alternating display pattern
- **Resource Validation**: Real-time checking of player resources for quest completion eligibility
- **Completion Logic**: Automatic resource deduction and reward distribution on quest completion
- **Menu Animation**: AnimationPlayer-based Open/Close animations matching other building menus
- **Translation Support**: Quest headers and descriptions use translation keys (QUEST_X_HEADER/DESCRIPTION)
- **Status Indicators**: Visual can/can't claim status sprites based on resource availability
- **Save Integration**: Quest progress persists through GameSaveData.QuestData with completed/activated lists

## Technical Implementation

### Save/Load System
**Complete persistence with automatic state management:**

**SaveHandler**: Generic JSON serialization using System.Text.Json and Godot FileAccess
**ResourcesManager**: Autoload singleton managing all game data with auto-save every 1 minute
**Data Models**: PlayerStats, PlayerResources, WorldData, GameSettings, QuestData with type-safe enums

**Integration**: PlayerController auto-loads/saves position, direction, tool. CustomDataLayerManager handles world data.

### Resource Management
**TextureManager**: Autoload singleton with dual texture system
- **Regular Textures**: For UI, inventory (WoodTexture, StoneTexture)
- **Icon Textures**: For dropped items (WoodIconTexture, StoneIconTexture)
- **Method**: `GetResourceTexture()` and `GetResourceIconTexture()` with ResourceType enum

**Dropped Resource System**: Complete persistence and animation
- **DroppedResourceData**: Position, ResourceType, Quantity stored in WorldData
- **DroppedResourceManager**: Loads saved resources on game start
- **Auto-Registration**: Resources register/unregister with ResourcesManager on spawn/collect

### Region-Based Object Management
**Region1Manager**: Manages object spawning with configurable rates and probabilities
- **Spawn System**: Dynamic intervals (15s base + existing object count), max 30 objects
- **Spawn Probabilities**: 39% trees, 24% rocks, 20% berry bushes, 5% green bushes, 10% sandstone rocks, 2% brown mushrooms
- **Tile Integration**: Only spawns on `region=1` and `canDestroyableObject=true` tiles
- **Save/Load**: Object positions stored in CustomDataLayerManager, auto-restored on game start
- **Positioning**: Objects spawn centered in tiles (worldPos = tilePos * 16 + 8)
- **Dynamic Counting**: Calculates spawned objects dynamically rather than manual tracking
- **Performance Optimization**: Uses efficient GetTilesWhere method instead of nested loops

**Brown Mushroom System**: New harvestable resource across all regions
- **Spawn Rate**: 2% chance in regions 1-4 (reduced tree/rock spawn by 1% each)
- **Health**: 2 HP (fragile, easy to harvest)
- **Drops**: 1 brown mushroom + 1 leaf (total 2 items)
- **Integration**: Added to ObjectType enum and all region managers
- **Placement**: Uses BerryBush placement type for consistent tile management

### Signal-Based Tool System
**CommonSignals**: Centralized event system for tool interactions
- **Tool Signals**: PickaxeUsed, HammerUsed, SwordUsed, ToolUsed emit with tile position and power/damage values
- **Object Listening**: Trees, rocks, anvils listen to relevant tool signals
- **Position Validation**: Objects check if signal position matches their location
- **Decoupled Design**: Tools don't directly reference objects, enabling flexible interactions
- **Power System**: ResourcesManager provides GetPickaxeDamage() and GetHammerPower() methods
- **Enhanced Timing**: Sword signal now emits at 50% animation progress instead of end
- **Cooldown System**: Bow shooting limited to once per 1.5 seconds, berry consumption once per second

### Player Stats & Survival System
**PlayerStatsManager**: Autoload singleton managing player survival mechanics
- **Energy System**: 100 max energy, depletes by 1 per tool use, restores after 5s inactivity
- **Food & Water**: 100 max each, depletes every 2nd/3rd tool use respectively
- **Health System**: 100 max health, affected by starvation/dehydration
- **Speed Modifiers**: 60% speed when food OR water = 0, 40% speed when both = 0
- **Animation Speed**: Matches movement speed modifiers for consistent feel
- **Danger Overlay**: Red screen overlay when food/water = 0 (15% alpha single, 25% both)
- **Starvation Timer**: Player dies after 30 seconds at 0 food or water
- **Berry Consumption**: Restores +5 food, +3 water with 1-second cooldown

### Enhanced Rock System
**Ore Rock Variants**: 8 specialized rock types for mining progression
- **CopperRock**: Drops CopperOre, basic tier mining
- **IronRock**: Drops IronOre, essential for strong equipment
- **GoldRock**: Drops GoldOre, valuable for luxury items
- **IndigosiumRock**: Drops IndigosiumOre, magical properties
- **MithrilRock**: Drops MithrilOre, legendary lightweight metal
- **ErithrydiumRock**: Drops ErithrydiumOre, fire-energy infused
- **AdamantiteRock**: Drops AdamantiteOre, nearly indestructible
- **UraniumRock**: Drops UraniumOre, radioactive and dangerous
- **Consistent Mechanics**: All rocks have 12 HP, drop 2 ore, use same hit animations

**ObjectType Enum**: Organized classification system
- **Resources**: Tree=1 (8HP, 3 Wood), Rock=2 (12HP, 2 Stone)
- **Buildings**: House=10, Farm=11, Mine=12, CraftingStation=13
- **Plants**: Wheat=40, Carrot=41, Potato=42
- **Decorations**: Torch=60, Fence=61
- **Special**: Bridge=80, Portal=81

### Progress Bar System
**Dual Progress Bar Implementation**: Horizontal and vertical progress indicators
- **ProgressBar.tscn**: Horizontal progress (left-to-right) for building health
- **ProgressBarVertical.tscn**: Vertical progress (bottom-to-top) for crafting progress
- **Smart Visibility**: Health bars hidden when full, crafting bars hidden when not crafting
- **Texture System**: Uses progressBg.png/progressFront.png (health) and progressBgGreen.png/progressFrontGreen.png (crafting)
- **Region Cropping**: Efficient texture region manipulation for smooth progress display
- **Animation Support**: Tween-based progress animations with customizable duration

### Localization System
**Multi-Language Support**: CSV-based translation system with 5 languages
- **Supported Languages**: English, Spanish, French, German, Italian
- **Translation Files**: translations.csv with key-value pairs for all UI text
- **Resource Descriptions**: WOOD_TEXT, STONE_TEXT, PLANK_TEXT with localized names
- **Item Descriptions**: WOOD_DESCRIPTION, PLANK_DESCRIPTION with detailed explanations
- **Godot Integration**: Uses Godot's built-in localization system with CSV import

## Key Enums & Data Types

**ResourceType**: Wood=1, Stone=2, Net=3, Plank=4, Stone2=5, Berry=6, Leaf=7, CopperOre=8, IronOre=9, GoldOre=10, IndigosiumOre=11, MithrilOre=12, ErithrydiumOre=13, AdamantiteOre=14, UraniumOre=15, CopperBar=16, IronBar=17, GoldBar=18, IndigosiumBar=19, MithrilBar=20, ErithrydiumBar=21, AdamantiteBar=22, UraniumBar=23, CopperSheet=24, IronSheet=25, GoldSheet=26, IndigosiumSheet=27, MithrilSheet=28, ErithrydiumSheet=29, AdamantiteSheet=30, UraniumSheet=31, WoodenBeam=32, WoodenStick=33, RawRabbitLeg=34, CookedRabbitLeg=35, WoodenKey=36
**ToolType**: None, Pickaxe, Sword, Bow, Hammer, Hoe, WateringCan (Shovel removed)
**ObjectType**: Tree=1, Rock=2, Anvil=20, Campfire=21, Furnace1=22, Furnace2=23, Furnace3=24, Furnace4=25, House=10, Wheat=40, Torch=60, Bridge=80

**QuickSelectItem**: Data structure for SelectedToolPanel slots
- `IsEmpty` (bool): Whether slot contains an item
- `IsTool` (bool): True for tools, false for resources
- `ToolType`/`ResourceType`: Item type and quantity

## File Structure

**Core Systems**:
- `scenes/ResourcesManager.cs/.tscn` - Save/load and resource management
- `scenes/TextureManager.cs/.tscn` - Dual texture system for resources + tools
- `scenes/CustomDataLayerManager.cs/.tscn` - Tile data management
- `scenes/DroppedResourceManager.cs/.tscn` - Dropped resource persistence
- `scenes/BuildingPlacer.cs/.tscn` - Building placement system with validation
- `scenes/BuildingManager.cs/.tscn` - Building lifecycle management
- `scenes/PlayerStatsManager.cs/.tscn` - Player survival stats and energy system

**UI Systems**:
- `scenes/UI/SelectedToolPanel.cs/.tscn` - 10-slot quick select panel with animations
- `scenes/UI/inventory/InventoryMenu.cs/.tscn` - Multi-sector inventory with item descriptions
- `scenes/UI/buildingMenus/AnvilMenu.cs/.tscn` - Per-anvil crafting interface
- `scenes/UI/buildingMenus/CampfireMenu.cs/.tscn` - Campfire cooking interface with recipe selection
- `scenes/UI/buildingMenus/Furnace1Menu.cs/.tscn` - Furnace1 smelting interface with copper/iron bar production
- `scenes/UI/buildingMenus/Furnace2Menu.cs/.tscn` - Furnace2 smelting interface with gold/indigosium bar production
- `scenes/UI/buildingMenus/Furnace3Menu.cs/.tscn` - Furnace3 smelting interface with mithril/erithrydium bar production
- `scenes/UI/buildingMenus/Furnace4Menu.cs/.tscn` - Furnace4 smelting interface with adamantite/uranium bar production
- `scenes/UI/progress/ProgressBar.cs/.tscn` - Horizontal progress bars (health)
- `scenes/UI/progress/ProgressBarVertical.cs/.tscn` - Vertical progress bars (crafting)
- `scenes/UI/mapPanel/MapButton.cs/.tscn` - Map button for opening region unlock interface
- `scenes/UI/mapPanel/MapPanel.cs/.tscn` - Full-screen map with region unlock buttons
- `scenes/UI/mapPanel/RegionUnlockButton.cs/.tscn` - Individual region unlock controls with gold costs
- `scenes/PlayerController.cs` - Tool usage and resource consumption (no tool selection)

**Objects**:
- `scenes/mapObjects/Tree.cs/.tscn` - Destructible trees (IDestroyableObject)
- `scenes/mapObjects/Rock.cs/.tscn` - Destructible rocks (IDestroyableObject)
- `scenes/mapObjects/DroppedResource.cs/.tscn` - Animated resource drops
- `scenes/mapObjects/buildings/Anvil.cs/.tscn` - Crafting buildings with health and progress systems
- `scenes/mapObjects/buildings/Bridge.cs/.tscn` - Water-crossing bridges with enhanced hit animations
- `scenes/mapObjects/buildings/Campfire.cs/.tscn` - Cooking station with recipe system and progress tracking (1x2 vertical)
- `scenes/mapObjects/buildings/Furnace1.cs/.tscn` - Copper/Iron bar smelting station (2x2)
- `scenes/mapObjects/buildings/Furnace2.cs/.tscn` - Gold/Indigosium bar smelting station (2x2)
- `scenes/mapObjects/buildings/Furnace3.cs/.tscn` - Mithril/Erithrydium bar smelting station (2x2)
- `scenes/mapObjects/buildings/Furnace4.cs/.tscn` - Adamantite/Uranium bar smelting station (2x2)
- `scenes/mapObjects/animals/Rabbit.cs/.tscn` - Huntable animals that drop raw meat resources
- `scenes/mapObjects/Chest.cs/.tscn` - Interactive treasure chests with key-based unlocking and rewards
- `scenes/regions/RegionBlocker.cs/.tscn` - Region access control with unlock progression
- `scenes/npcs/TutorialNPC.cs/.tscn` - Interactive tutorial guide with quest system and typewriter effects
- `scenes/regions/region2/QuestBoard.cs/.tscn` - Interactive quest board with R-key activation and player detection

**Management**:
- `scenes/regions/Region1Manager.cs/.tscn` - Object spawning and management
- `scenes/RegionUnlockManager.cs` - Region unlock progression and gold-based purchasing
- `scenes/ChestManager.cs` - Chest persistence and loading system
- `scripts/GameData.cs` - Data models and save structures (includes ChestSaveData, ResourceReward)
- `scripts/SaveHandler.cs` - JSON serialization utilities
- `scripts/ObjectType.cs` - Object classification enum
- `scripts/ResourceType.cs` - Resource classification enum (includes WoodenKey)
- `scripts/ToolType.cs` - Tool classification enum
- `scripts/CommonSignals.cs` - Centralized signal management
- `scripts/QuestManager.cs` - Static quest management with predefined quests and completion logic
- `scenes/UI/buildingMenus/QuestsMenu.cs/.tscn` - Quest menu UI with dynamic quest display and completion

## Development Status

### ✅ Completed Systems
1. **Core Movement**: Dynamic speed modification based on tile properties
2. **Tool Animation**: 7-tool system with directional animations and state management
3. **Save/Load**: Robust persistence with ResourcesManager and automatic state collection
4. **Custom Data Layers**: Multi-layer tile data management with JSON serialization
5. **Resource Objects**: Trees and rocks with IDestroyableObject interface
6. **Resource Dropping**: ID-based persistence system with instant collection and scale animation
7. **Texture Management**: Dual texture system (regular + icon textures for resources + tools)
8. **Region Management**: Region1Manager with configurable object spawning
9. **Player Interaction**: Tile placeholder system and tool-based interactions
10. **Arrow Shooting**: Bow mechanics with 180° restriction and direction indicators
11. **SelectedToolPanel UI**: 10-slot quick select system with keyboard/mouse input, animations, and unassign functionality
12. **Resource Usage System**: Instant resource consumption (Net) vs animated tool usage
13. **Tool/Resource Separation**: Clear distinction between tools (animated) and resources (instant)
14. **Inventory Menu System**: Multi-sector inventory with item selection and animations
15. **Item Information System**: Static ItemInformation class with descriptions and usage flags
16. **Item Description Panel**: Interactive panel with Use/Remove/Quick Action buttons
17. **Common Signals System**: Centralized signal management for resource usage events
18. **Building System**: Complete building placement, validation, health, and repair mechanics
19. **Anvil Crafting System**: Per-anvil crafting with resource transformation and progress tracking
20. **Progress Bar System**: Dual horizontal/vertical progress bars for health and crafting
21. **Multi-Anvil Support**: Independent crafting state per anvil with persistent recipe selection
22. **Resource Transformation**: Wood → Plank crafting with hammer power requirements
23. **Region Blocker System**: Dynamic region unlocking with fade-out animations and save persistence
24. **Enhanced Bridge Animations**: Improved hit effects with color flash and scale animations
25. **Campfire Cooking System**: Complete food preparation with recipes, timers, and progress tracking
26. **Tutorial NPC System**: 7-stage guided tutorial with dialog system and quest progression
27. **Hunting & Food System**: Rabbit hunting with raw/cooked meat resources and cooking mechanics
28. **Extended Resource Types**: Added RawRabbitLeg and CookedRabbitLeg with full texture support
29. **Signal-Based Tutorial**: Building and hunting events automatically advance tutorial quests
30. **Typewriter Dialog System**: Character-by-character text animation with 0.1s delay and skip functionality
31. **Interactive Chest System**: Key-based treasure chests with configurable rewards and animation integration
32. **Map & Region Unlock Interface**: Complete map system with gold-based region purchasing
33. **Sequential Region Unlocking**: Regions unlock in order with automatic (tutorial) and manual (gold) methods
34. **Enhanced Resource System**: Added WoodenKey resource type with full texture and icon support
35. **Chest Persistence System**: Complete save/load integration for chest data and opened states
36. **Multi-Tile Building Expansion**: Campfire (1x2 vertical) and Furnace1 (2x2) with advanced placement logic
37. **Enhanced Food Consumption**: Unified 0.5s timeout system for all consumable food items
38. **Complete Multi-Furnace System**: Four-tier furnace system (Furnace1-4) for all metal bar production
39. **Unified Smelting Recipes**: All furnaces use 3 ore + 1 wood → 1 bar with 3-second processing
40. **Advanced Building Menu**: All four furnaces integrated with affordability checking and resource validation
41. **Ore-Based Building Costs**: Furnaces require specific ore combinations instead of processed materials
42. **Quest System**: Complete quest board interaction with R-key activation, dynamic quest menu, and reward distribution

### 🚧 In Progress
1. **Multi-Region Expansion**: Additional region managers for different biomes
2. **Farming System**: Plant growth stages and watering mechanics
3. **Advanced Crafting**: Additional recipes and crafting buildings

### 📋 Planned Features
1. **Combat System**: Enemy AI, sword interactions, magic abilities
2. **Advanced Crafting**: Automated production buildings and resource chains
3. **Skills System**: Unlockable abilities and passive bonuses
4. **Day/Night Cycle**: Time-based gameplay mechanics

## Architecture Benefits

### Extensibility
- **IDestroyableObject Interface**: Add new harvestable objects without changing PlayerController
- **Signal-Based Tools**: Easy to add new tools and objects without tight coupling
- **Region System**: Easy to add new biomes with unique object spawning
- **Enum-Based Design**: Type-safe object classification and resource management
- **Modular Components**: Reusable systems for save/load, textures, and object management
- **Per-Instance Systems**: Each anvil has independent crafting state and menus

### Performance
- **Efficient Spawning**: Region-based object management with configurable limits
- **Smart Persistence**: Only save essential data, automatic cleanup on collection
- **Optimized Animations**: Tool visibility only during use, efficient tween management
- **Tile-Based Logic**: Fast tile property lookups for movement and interaction validation
- **Dynamic Progress**: Progress bars only visible when needed, efficient region cropping

### Maintainability
- **Clean Separation**: Clear boundaries between systems (save/load, spawning, interaction, crafting)
- **Type Safety**: Enums prevent string-based errors, compile-time validation
- **Consistent Patterns**: Standardized approaches for object creation, animation, and persistence
- **Future-Proof**: Interface-based design allows easy extension without breaking changes
- **Signal Decoupling**: Tools and objects communicate through signals, not direct references
- **Scene-Based Design**: UI components embedded in scenes for easy visual editing