1. You added furnace 2,3,4. but you didnt create scenes for them (and furnace menus) - add them and assign scripts. scripts should derived by CanvasLayer, at least those for menus.

2. Design a day/night system. Player and campfire should have a lighting around then (i want to be able to control radius of it). Day should take 5 minutes and night 3 minutes (this should be configurable). Anight it should be dark (visible, but dark, i want to be able to configure how dark). Research what solutions are possible and select the best one. Implement it with my requirements.
3. You can use existing scene that i added - DayNight.tscn. I will add it to world scene. Also, this scene has labels: LabelTime and LabelAmPm - update them to show current time (time of game world).