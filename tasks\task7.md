Implement the following features for the crafting survival game:

## 1. Tutorial NPC Typewriter Text Effect
- Modify the TutorialNPC dialog system to display text character-by-character with a typewriter effect
- Use a 0.1 second delay between each character
- Currently the NPC shows all dialog text instantly - change this to animate each letter appearing sequentially
- Ensure the player can still advance dialog normally after the typewriter effect completes

## 2. TextureManager Resource Additions
- Add a new ResourceType enum value: `Wooden<PERSON>ey`
- Add the WoodenKey resource and icon to TextureManager (user will set actual texture later)
- Add a Coin icon to TextureManager (user will set actual texture later)
- Ensure both new resources follow the existing TextureManager pattern for resource icons

## 3. Interactive Chest System Implementation
- Use the existing Chest.tscn scene which contains:
  - AnimationPlayer with "OpenChest" animation
  - PlayerDetection Area2D node
- Configure PlayerDetection collision mask/layer to match Player.tscn->PlayerDetector for proper interaction
- Create a Chest.cs script with the following features:

### Chest Properties:
- ChestType enum (WoodenChest, StoneChest) - for now only implement WoodenChest
- Export texture properties for different chest types
- GoldAmount (int) - amount of gold to give player
- List of ResourceReward (custom class with ResourceType and quantity)

### Chest Behavior:
- Player can press 'R' when near chest to attempt opening
- Opening requires correct key type in inventory (WoodenChest requires WoodenKey)
- If player has key: consume the key, play OpenChest animation, then spawn rewards
- After animation completes: spawn gold and resources as dropped items (similar to Rock destruction)
- After spawning rewards: destroy the chest (QueueFree)
- If player lacks key: print debug info

### Resource Spawning:
- Follow the same pattern as Rock.cs for dropping resources around the chest location
- Spawn gold as dropped items
- Spawn all configured resources as dropped items

## 4. Tutorial NPC Completion Reward
- When tutorial quest reaches Complete state, modify TutorialNPC to:
  - Disappear (hide or destroy the NPC)
  - Spawn a WoodenChest at the NPC's location with 50 gold
- Ensure the spawned chest persists through save/load cycles:
  - Add chest data to the save system (GameSaveData)
  - Save chest position, type, contents, and state
  - Restore chests on game load if they haven't been opened yet

## Technical Requirements:
- Follow existing code patterns from Rock.cs for resource dropping
- Use the established save/load system for persistence
- Maintain consistency with existing interaction systems (R key usage)
- Ensure proper collision detection setup for player interaction
- Add appropriate debug logging for testing