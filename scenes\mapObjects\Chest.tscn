[gd_scene load_steps=8 format=3 uid="uid://cg1oqmy0aoln5"]

[ext_resource type="Script" uid="uid://vgurskpnl7t4" path="res://scenes/mapObjects/Chest.cs" id="1_chest_script"]
[ext_resource type="Texture2D" uid="uid://k7p0aoam6uxm" path="res://resources/solaria/buildings/animated/Chest 09-Sheet.png" id="1_hkfav"]

[sub_resource type="Animation" id="Animation_1m86o"]
resource_name = "OpenChest"
length = 0.6
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5]
}

[sub_resource type="Animation" id="Animation_1fdf7"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_2cgng"]
_data = {
&"OpenChest": SubResource("Animation_1m86o"),
&"RESET": SubResource("Animation_1fdf7")
}

[sub_resource type="CircleShape2D" id="CircleShape2D_hkfav"]
radius = 16.0312

[sub_resource type="RectangleShape2D" id="RectangleShape2D_hkfav"]
size = Vector2(16, 9)

[node name="Chest" type="Node2D"]
script = ExtResource("1_chest_script")
WoodenChestTexture = ExtResource("1_hkfav")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_hkfav")
hframes = 12

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_2cgng")
}

[node name="PlayerDetection" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetection"]
shape = SubResource("CircleShape2D_hkfav")

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 3.5)
shape = SubResource("RectangleShape2D_hkfav")
