[gd_scene load_steps=4 format=3 uid="uid://bxhb17skm6mpd"]

[ext_resource type="Script" uid="uid://cpvqg1mx13bav" path="res://scenes/mapObjects/BlueMushroom.cs" id="1_bluemushroom"]
[ext_resource type="Texture2D" uid="uid://4ax3o4ttemf3" path="res://resources/solaria/exterior/Mushrooms1.png" id="2_it7pj"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_bluemushroom"]

[node name="BlueMushroom" type="Node2D"]
script = ExtResource("1_bluemushroom")
MaxHealth = 1

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_it7pj")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="ProgressBar" parent="." instance=ExtResource("3_bluemushroom")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
