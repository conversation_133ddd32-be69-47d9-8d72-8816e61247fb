using Godot;

public partial class PlayerLight : PointLight2D
{
	[Export] public float LightRadius { get; set; } = 64.0f;
	[Export] public Color LightColor { get; set; } = Colors.White;
	[Export] public float LightEnergy { get; set; } = 1.5f; // Moderate energy to counteract night darkness
	[Export] public bool OnlyShowAtNight { get; set; } = true;

	private DayNightManager _dayNightManager;

	public override void _Ready()
	{
		// Configure the light properties
		Enabled = true;
		Energy = LightEnergy;
		Color = LightColor;

		// Set texture scale based on radius
		if (Texture != null)
		{
			TextureScale = LightRadius / 64.0f;
		}

		// Find the DayNightManager
		_dayNightManager = GetNode<DayNightManager>("/root/world/DayNight");
		if (_dayNightManager == null)
		{
			// Try alternative path
			_dayNightManager = GetTree().GetFirstNodeInGroup("DayNightManager") as DayNightManager;
			if (_dayNightManager == null)
			{
				GD.PrintErr("PlayerLight: DayNightManager not found!");
			}
		}

		GD.Print($"PlayerLight: Initialized with radius {LightRadius}, energy {LightEnergy}, enabled {Enabled}");
	}

	public override void _Process(double delta)
	{
		if (_dayNightManager == null) return;

		// Show/hide light based on day/night cycle
		bool shouldBeVisible;
		if (OnlyShowAtNight)
		{
			shouldBeVisible = _dayNightManager.IsNight();
		}
		else
		{
			shouldBeVisible = true;
		}

		if (Visible != shouldBeVisible)
		{
			Visible = shouldBeVisible;
			GD.Print($"PlayerLight: Visibility changed to {Visible} (IsNight: {_dayNightManager.IsNight()})");
		}
	}

	public void SetLightRadius(float radius)
	{
		LightRadius = radius;
		if (Texture != null)
		{
			TextureScale = radius / 64.0f;
		}
		GD.Print($"PlayerLight: Radius set to {radius}");
	}

	public void SetLightColor(Color color)
	{
		LightColor = color;
		Color = color;
	}

	public void SetLightEnergy(float energy)
	{
		LightEnergy = energy;
		Energy = energy;
	}

	public void SetOnlyShowAtNight(bool nightOnly)
	{
		OnlyShowAtNight = nightOnly;
		GD.Print($"PlayerLight: OnlyShowAtNight set to {nightOnly}");
	}
}
