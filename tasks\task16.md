1. Add arrow ResourceType
2. Add to ItemInformation and add translations
3. Add to TextureManager
4. When player has selected a bow then i want to change logic - i want player to be able to shoot in any direction (not only 180 degrees from where he stands). When player shoots then you should calculate in which direction he shoots and adjust player's animation direction - so it player looks for example up but shoots down then he should look down when shooting and change his direction to down. Basically calculate if he shoots closest to up/down/right/left and adjust direction.
5. When player hits with sword i also want to calculate direction in which he hits (show placeholder with direction same as we use for bow) and adjust player's animation direction. he should hit in that selected direction (same as for bow). Make sure to change player's direction so that hit area is properly calculated - look how it's done now. but for sword we cand hit in other angle than up/down/right/left like with arrow.