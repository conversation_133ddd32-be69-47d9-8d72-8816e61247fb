1. FIX:
* when i use in inventory menu items like for example berries and i have 3 berries - i use one, selected placehotler is still visible - good, we have 2 left, i use one more, selected placeholder is still visible - good, but button use is disabled and i need to click berry again to be able to use it - wrong. when i use item and only one remains then this use button is unselected.

2. add 2% of chance to get a brown mushroom on region 1-4 managers, and reduce chance 1% of tree and 1% of rock spawn chance.

3. I want to add a new building - Grindstone - that can be built. It should work similar to Furnace1. Duplicate Furnace1 and Furnace1Menu (both scenes tscn and scripts cs). It will process mostly wood items. In Grindstone1Menu - i want to be able to craft plank from wood and from plank - wooden beam. They should cost the same as in anvil. Also, add this building to build menu. This building sprite has 4 frames and it's in resources->solaria->buildings->animated->grindstone.png. Animation should work when it's working - just like furnace. Building should be saved and loaded when game closes/opens - just like other buildings (it should also save state of currently processing items etc). The cost of building this Gringstone should be 5 wooden beams + 10 stone. If required - add translations.

4. Make sure project builds

5. Update technical.md with new changes
6. At the end, when you finish 1-5, verify whole project in terms of what is missing in technical.md and update technical.md with missing mechanics/logic information - like, verify everyhing and add there (but be descriptive, but add only clue and most important things, not too long, just an overwiew).