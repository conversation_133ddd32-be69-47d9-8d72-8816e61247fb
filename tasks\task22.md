1. I get error - fis: 
E 0:00:04:014   NodeExtensions.cs:47 @ T Godot.Node.GetNode<T>(Godot.NodePath): System.InvalidCastException: Unable to cast object of type 'Godot.Sprite2D' to type 'Godot.AnimationPlayer'.
  <C# Error>    System.InvalidCastException
  <C# Source>   /root/godot/modules/mono/glue/GodotSharp/GodotSharp/Core/Extensions/NodeExtensions.cs:47 @ T Godot.Node.GetNode<T>(Godot.NodePath)
  <Stack Trace> NodeExtensions.cs:47 @ T Godot.Node.GetNode<T>(Godot.NodePath)
                Shop.cs:28 @ void Shop._Ready()
                Node.cs:2546 @ bool Godot.Node.InvokeGodotClassMethod(Godot.NativeInterop.godot_string_name&, Godot.NativeInterop.NativeVariantPtrArgs, Godot.NativeInterop.godot_variant&)
                CanvasItem.cs:1654 @ bool Godot.CanvasItem.InvokeGodotClassMethod(Godot.NativeInterop.godot_string_name&, Godot.NativeInterop.NativeVariantPtrArgs, Godot.NativeInterop.godot_variant&)
                Node2D.cs:557 @ bool Godot.Node2D.InvokeGodotClassMethod(Godot.NativeInterop.godot_string_name&, Godot.NativeInterop.NativeVariantPtrArgs, Godot.NativeInterop.godot_variant&)
                Shop_ScriptMethods.generated.cs:88 @ bool Shop.InvokeGodotClassMethod(Godot.NativeInterop.godot_string_name&, Godot.NativeInterop.NativeVariantPtrArgs, Godot.NativeInterop.godot_variant&)
                CSharpInstanceBridge.cs:24 @ Godot.NativeInterop.godot_bool Godot.Bridge.CSharpInstanceBridge.Call(nint, Godot.NativeInterop.godot_string_name*, Godot.NativeInterop.godot_variant**, int, Godot.NativeInterop.godot_variant_call_error*, Godot.NativeInterop.godot_variant*)
2. Campfire menu - ESC key doesn't close campfire menu
3. When tutorial npc is at the end and spawns chest - chest didn't add gold. Verify, maybe i fixed it with my other changes, but verify if you cant see any error
4. When resource is spawned (dropped resource) then sometimes when player is in range - before it gets to full size (item is scalled to full size) - then sometimes its collected by player and it starts scalling down and player collects it. But i want this item to be first fully scalled because player sometimes can't see item and it's already collected - so scale to full size and then scale to 0 and collect.
5. Dropped resources have colleciton area range - i want to do something like this: when i have more than 1 resource of same type spawned and they are in range of each other then i want them to "merge" so that there is a single item that when i collect it then i get amount of all items. For example, if i have 3 wooden plank items spawned and they are in range of each other then i want them to merge into single (make sure a single, not duplicated!) item that when i collect it then i get 3 wooden planks. When they are merged (so we have more than 1 object in a single collectable dropped resource) - then in DroppedResource set LabelAmount to number of items that player will collect it. This is because i dont want to have a lot of same objects spawned one close to another. Also, make sure that when save/load game - this amount is applied. When they are "merging" then play animation that they move to single position and then merge to one.
6. When player comes to spawned chest and clicks "R" and he does not have a key - then player movement is blocked and he can't move. When he does not have a key then don't block player movement.s