using Godot;
using System.Linq;

public partial class RegionUnlockButton : Node2D
{
	private Button _unlockButton;
	private Label _labelBuy;
	private Label _labelPrice;
	private Sprite2D _buttonSprite;
	private Sprite2D _waterSprite;

	[Export] public int RegionId { get; set; } = 4;
	[Export] public int UnlockPrice { get; set; } = 10000;
	private bool _isUnlocked = false;
	private int _lastUnlocked = 0;
	
	public override void _Ready()
	{
		_unlockButton = GetNode<Button>("UnlockButton");
		_labelBuy = GetNode<Label>("Button/LabelBuy");
		_labelPrice = GetNode<Label>("Button/LabelPrice");
		_buttonSprite = GetNode<Sprite2D>("Button");
		_waterSprite = GetNode<Sprite2D>("Water");

		if (_unlockButton != null)
		{
			_unlockButton.Pressed += OnUnlockButtonPressed;
		}

		CheckUnlockStatus();
		UpdateVisualState();
	}

	public override void _ExitTree()
	{
		if (_unlockButton != null)
		{
			_unlockButton.Pressed -= OnUnlockButtonPressed;
		}
	}

	private void CheckUnlockStatus()
	{
		if (GameSaveData.Instance.UnlockedRegions != null)
		{
			_isUnlocked = GameSaveData.Instance.UnlockedRegions.Contains(RegionId);
			_lastUnlocked = GameSaveData.Instance.UnlockedRegions.Max();
		}

		if (_labelPrice != null)
		{
			_labelPrice.Text = UnlockPrice.ToString();
		}
	}

	private void OnUnlockButtonPressed()
	{
		if (_isUnlocked)
		{
			GD.Print($"RegionUnlockButton: Region {RegionId} is already unlocked");
			return;
		}

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null)
		{
			GD.PrintErr("RegionUnlockButton: ResourcesManager not found");
			return;
		}

		int currentMoney = GameSaveData.Instance.PlayerStats.Money;
		if (currentMoney < UnlockPrice)
		{
			GD.Print($"RegionUnlockButton: Not enough money. Need {UnlockPrice}, have {currentMoney}");
			return;
		}

		GameSaveData.Instance.PlayerStats.Money = Mathf.Max(0, GameSaveData.Instance.PlayerStats.Money - UnlockPrice);

		var regionUnlockManager = RegionUnlockManager.Instance;
		if (regionUnlockManager != null)
		{
			regionUnlockManager.UnlockRegionWithGold(RegionId);
			_isUnlocked = true;
			UpdateVisualState();
			GD.Print($"RegionUnlockButton: Successfully unlocked region {RegionId} for {UnlockPrice} gold");
		}
		else
		{
			GameSaveData.Instance.PlayerStats.Money = Mathf.Max(0, GameSaveData.Instance.PlayerStats.Money + UnlockPrice);
			GD.PrintErr("RegionUnlockButton: RegionUnlockManager not found, refunding money");
		}
	}

	private void UpdateVisualState()
	{
		if (_isUnlocked)
		{
			QueueFree();
			if (_labelBuy != null)
				_labelBuy.Text = "UNLOCKED";
			
			if (_unlockButton != null)
				_unlockButton.Disabled = true;

			if (_buttonSprite != null)
				_buttonSprite.Modulate = new Color(0.7f, 0.7f, 0.7f, 1.0f);
		}
		else
		{
			if(_lastUnlocked < RegionId - 1)
			{
				_waterSprite.Visible = true;
				_buttonSprite.Visible = false;
				_unlockButton.Disabled = true;
			}
			else if(_lastUnlocked < RegionId)
			{
				_waterSprite.Visible = false;
				_buttonSprite.Visible = true;
				_unlockButton.Disabled = false;
			}
			else
			{
				QueueFree();
			}
		}
	}
}
