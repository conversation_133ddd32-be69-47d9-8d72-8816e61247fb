[gd_scene load_steps=21 format=3 uid="uid://jglbfpyl6t71"]

[ext_resource type="Script" uid="uid://cqd7ky2umaym6" path="res://scenes/UI/mapPanel/MapPanel.cs" id="1_mappanel_script"]
[ext_resource type="Texture2D" uid="uid://bveehcbtq0yl3" path="res://resources/solaria/UI/unlockMap/unlockMapBackgroundPanel.png" id="1_ve4te"]
[ext_resource type="Texture2D" uid="uid://t68v8smphaf" path="res://resources/solaria/UI/unlockMap/unlockMapForegroundPanel.png" id="2_fl801"]
[ext_resource type="PackedScene" uid="uid://g2k5372rsr2t" path="res://scenes/UI/mapPanel/RegionUnlockButton.tscn" id="3_qnd5j"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="4_m0yx0"]

[sub_resource type="Animation" id="Animation_iw7eh"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_r0oy0"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_cna2i"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_v0ypl"]
_data = {
&"Close": SubResource("Animation_iw7eh"),
&"Open": SubResource("Animation_r0oy0"),
&"RESET": SubResource("Animation_cna2i")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_heghj"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3j5fk"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_urush"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iinwl"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rfau4"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_2ic56"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_a7i3p"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_qgpo1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_r0v4x"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_fblwf"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_qw5bi"]

[node name="MapPanel" type="CanvasLayer"]
script = ExtResource("1_mappanel_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_v0ypl")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
scale = Vector2(0.95, 0.95)
texture = ExtResource("1_ve4te")

[node name="PanelInternal" type="Sprite2D" parent="Control/Panel"]
scale = Vector2(0.68, 0.68)
texture = ExtResource("2_fl801")

[node name="RegionUnlockButton39" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-81, -82)
RegionId = 40

[node name="RegionUnlockButton38" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-50, -82)
RegionId = 39

[node name="RegionUnlockButton37" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-82, -49)
RegionId = 38

[node name="RegionUnlockButton36" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-48, -49)
RegionId = 37

[node name="RegionUnlockButton35" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-81, -16)
RegionId = 36

[node name="RegionUnlockButton34" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-48, -15)
RegionId = 35

[node name="RegionUnlockButton33" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-82, 93)
RegionId = 34

[node name="RegionUnlockButton32" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-49, 93)
RegionId = 33

[node name="RegionUnlockButton31" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-81, 59)
RegionId = 32

[node name="RegionUnlockButton30" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-49, 60)
RegionId = 31

[node name="RegionUnlockButton29" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-82, 27)
RegionId = 30

[node name="RegionUnlockButton28" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-49, 28)
RegionId = 29

[node name="RegionUnlockButton27" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-17, 124)
RegionId = 28

[node name="RegionUnlockButton26" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(14, 124)
RegionId = 27

[node name="RegionUnlockButton25" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-17, 92)
RegionId = 26

[node name="RegionUnlockButton24" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(14, 92)
RegionId = 25

[node name="RegionUnlockButton23" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-18, 59)
RegionId = 24

[node name="RegionUnlockButton22" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(14, 59)
RegionId = 23

[node name="RegionUnlockButton21" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(91, 91)
RegionId = 22

[node name="RegionUnlockButton20" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(61, 91)
RegionId = 21

[node name="RegionUnlockButton19" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(91, 59)
RegionId = 20

[node name="RegionUnlockButton18" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(60, 59)
RegionId = 19

[node name="RegionUnlockButton17" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(91, 26)
RegionId = 18

[node name="RegionUnlockButton16" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(61, 26)
RegionId = 17

[node name="RegionUnlockButton15" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(91, -83)
RegionId = 16

[node name="RegionUnlockButton14" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(61, -83)
RegionId = 15

[node name="RegionUnlockButton13" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(91, -50)
RegionId = 14

[node name="RegionUnlockButton12" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(60, -49)
RegionId = 13

[node name="RegionUnlockButton11" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(93, -7)
RegionId = 12

[node name="RegionUnlockButton10" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(61, -6)
RegionId = 11

[node name="RegionUnlockButton9" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-17, -114)
RegionId = 10

[node name="RegionUnlockButton8" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(27, -114)
RegionId = 9

[node name="RegionUnlockButton7" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-16, -83)
RegionId = 8

[node name="RegionUnlockButton6" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(28, -83)
RegionId = 7

[node name="RegionUnlockButton5" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-15, -50)
RegionId = 6

[node name="RegionUnlockButton4" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(28, -50)
RegionId = 5

[node name="RegionUnlockButton3" parent="Control/Panel" instance=ExtResource("3_qnd5j")]
position = Vector2(-15.7895, 26.3158)
UnlockPrice = 50

[node name="CloseButtonSprite" type="Sprite2D" parent="Control/Panel"]
position = Vector2(114, -147)
texture = ExtResource("4_m0yx0")

[node name="CloseButton" type="Button" parent="Control/Panel"]
offset_left = 104.0
offset_top = -159.0
offset_right = 124.0
offset_bottom = -137.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_heghj")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_3j5fk")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_urush")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_iinwl")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_rfau4")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_2ic56")
theme_override_styles/hover = SubResource("StyleBoxEmpty_a7i3p")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_qgpo1")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_r0v4x")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_fblwf")
theme_override_styles/normal = SubResource("StyleBoxEmpty_qw5bi")
