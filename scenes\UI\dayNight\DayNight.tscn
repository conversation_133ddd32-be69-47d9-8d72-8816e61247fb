[gd_scene load_steps=4 format=3 uid="uid://7kuppoo036sw"]

[ext_resource type="Texture2D" uid="uid://dd3mhunjshdho" path="res://resources/solaria/UI/dayNight.png" id="1_0iv11"]
[ext_resource type="Script" uid="uid://nxe7617gsneq" path="res://scenes/UI/dayNight/DayNightManager.cs" id="1_script"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="2_fb8og"]

[node name="DayNight" type="CanvasLayer"]
script = ExtResource("1_script")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -40.0
offset_bottom = 40.0
grow_horizontal = 0

[node name="DayNightProgress" type="Sprite2D" parent="Control"]
position = Vector2(1, 25)
texture = ExtResource("1_0iv11")
hframes = 6

[node name="LabelTime" parent="Control" instance=ExtResource("2_fb8og")]
layout_mode = 0
offset_left = -14.0
offset_top = 25.0
offset_right = 51.0
offset_bottom = 42.0
scale = Vector2(0.46, 0.46)
text = "12:00"

[node name="LabelAmPm" parent="Control" instance=ExtResource("2_fb8og")]
layout_mode = 0
offset_left = -13.0
offset_top = 30.0
offset_right = 52.0
offset_bottom = 47.0
scale = Vector2(0.46, 0.46)
text = "AM"
