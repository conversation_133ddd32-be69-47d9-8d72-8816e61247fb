using Godot;
using System;

/// <summary>
/// Manages the quick select tool panel with 10 slots for tools and resources
/// <PERSON><PERSON> keyboard input (1-0), mouse clicks, and animations
/// </summary>
public partial class SelectedToolPanel : CanvasLayer
{
	private const int SLOT_COUNT = 10;

	// UI elements for each slot
	private Sprite2D[] _itemContainers = new Sprite2D[SLOT_COUNT];
	private Sprite2D[] _itemSprites = new Sprite2D[SLOT_COUNT];
	private Sprite2D[] _selectedPlaceholders = new Sprite2D[SLOT_COUNT];
	private Button[] _buttons = new Button[SLOT_COUNT];
	private AnimationPlayer[] _animationPlayers = new AnimationPlayer[SLOT_COUNT];
	private Sprite2D[] _unassignSprites = new Sprite2D[SLOT_COUNT];
	private Button[] _unassignButtons = new Button[SLOT_COUNT];

	private int _currentSelectedSlot = -1;
	private bool _isSelecting = false;

	public override void _Ready()
	{
		// Initialize UI references
		InitializeSlotReferences();

		// Subscribe to resource changes
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.ResourceChanged += OnResourceChanged;
		}

		// Load and display current quick select items
		RefreshAllSlots();

		// Select first slot by default if it has an item
		SelectSlot(0);
	}

	/// <summary>
	/// Initialize references to all slot UI elements
	/// </summary>
	private void InitializeSlotReferences()
	{
		// Item names: Item1, Item2, ..., Item9, Item0 (for slots 0-9)
		string[] itemNames = { "Item1", "Item2", "Item3", "Item4", "Item5", "Item6", "Item7", "Item8", "Item9", "Item0" };

		for (int i = 0; i < SLOT_COUNT; i++)
		{
			string itemName = itemNames[i];
			string basePath = $"Control/Panel/{itemName}";

			_itemContainers[i] = GetNode<Sprite2D>(basePath);
			_itemSprites[i] = GetNode<Sprite2D>($"{basePath}/ResourceIcon");
			_selectedPlaceholders[i] = GetNode<Sprite2D>($"{basePath}/SelectedPlaceholder");
			_buttons[i] = GetNode<Button>($"{basePath}/Button");
			_animationPlayers[i] = GetNode<AnimationPlayer>($"{basePath}/AnimationPlayer");
			_unassignSprites[i] = GetNode<Sprite2D>($"{basePath}/UnassignSprite");
			_unassignButtons[i] = GetNode<Button>($"{basePath}/UnassignButton");

			// Connect mouse click events
			int slotIndex = i; // Capture for closure
			_buttons[i].Pressed += () => OnSlotClicked(slotIndex);
			_unassignButtons[i].Pressed += () => OnUnassignButtonClicked(slotIndex);

			// Initially hide all selected placeholders and unassign elements
			_selectedPlaceholders[i].Visible = false;
			_unassignSprites[i].Visible = false;
			_unassignButtons[i].Visible = false;
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			// Handle number keys 1-0 for slot selection
			int slotIndex = GetSlotIndexFromKey(keyEvent.Keycode);
			if (slotIndex >= 0)
			{
				SelectSlot(slotIndex);
				GetViewport().SetInputAsHandled();
			}
		}
	}

	/// <summary>
	/// Convert key code to slot index (1-9 = 0-8, 0 = 9)
	/// </summary>
	private int GetSlotIndexFromKey(Key keycode)
	{
		return keycode switch
		{
			Key.Key1 => 0,
			Key.Key2 => 1,
			Key.Key3 => 2,
			Key.Key4 => 3,
			Key.Key5 => 4,
			Key.Key6 => 5,
			Key.Key7 => 6,
			Key.Key8 => 7,
			Key.Key9 => 8,
			Key.Key0 => 9,
			_ => -1
		};
	}

	/// <summary>
	/// Handle mouse click on a slot
	/// </summary>
	private void OnSlotClicked(int slotIndex)
	{
		SelectSlot(slotIndex);
	}

	/// <summary>
	/// Select a slot and play its animation
	/// </summary>
	private void SelectSlot(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= SLOT_COUNT) return;

		// Prevent multiple simultaneous selections
		if (_isSelecting) return;
		_isSelecting = true;

		var item = ResourcesManager.Instance?.GetQuickSelectItem(slotIndex);
		if (item == null || item.IsEmpty)
		{
			_isSelecting = false;
			return;
		}

		// Update current selection
		_currentSelectedSlot = slotIndex;

		// Hide all selected placeholders first
		for (int i = 0; i < SLOT_COUNT; i++)
		{
			_selectedPlaceholders[i].Visible = false;
		}

		// Play selection animation for this slot
		_animationPlayers[slotIndex].Play("select");

		// Handle tool/resource selection
		if (item.IsTool)
		{
			// Set tool in PlayerController
			var player = GetNode<PlayerController>("/root/world/Player");
			if (player != null)
			{
				player.SetCurrentTool(item.ToolType);
			}
		}
		else
		{
			// Handle resource selection (future implementation)
			// For now, just show it's selected
			GD.Print($"Selected resource: {item.ResourceType} (Quantity: {item.Quantity})");
		}

		// Reset selection flag after a short delay to allow animation to start
		GetTree().CreateTimer(0.05f).Timeout += () => _isSelecting = false;
	}

	/// <summary>
	/// Refresh all slot displays
	/// </summary>
	private void RefreshAllSlots()
	{
		for (int i = 0; i < SLOT_COUNT; i++)
		{
			RefreshSlot(i);
		}
	}

	/// <summary>
	/// Refresh a specific slot display
	/// </summary>
	private void RefreshSlot(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= SLOT_COUNT) return;

		var item = ResourcesManager.Instance?.GetQuickSelectItem(slotIndex);
		if (item == null)
		{
			// Clear slot
			_itemSprites[slotIndex].Texture = null;
			return;
		}

		if (item.IsEmpty)
		{
			// Empty slot
			_itemSprites[slotIndex].Texture = null;
		}
		else if (item.IsTool)
		{
			// Tool slot
			var texture = TextureManager.Instance?.GetToolTexture(item.ToolType);
			_itemSprites[slotIndex].Texture = texture;
		}
		else
		{
			// Resource slot - for now just show the icon, quantity display can be added later
			var texture = TextureManager.Instance?.GetResourceIconTexture(item.ResourceType);
			_itemSprites[slotIndex].Texture = texture;
			// TODO: Add quantity display when UI supports it
		}
	}

	/// <summary>
	/// Handle resource changes to update quantities and remove empty resources
	/// </summary>
	private void OnResourceChanged(object sender, ResourceChangedEventArgs e)
	{
		// Update resource quantities in quick select
		ResourcesManager.Instance?.UpdateQuickSelectResourceQuantities();

		// Refresh all slots to show updated quantities
		RefreshAllSlots();
	}

	/// <summary>
	/// Get the currently selected slot index
	/// </summary>
	public int GetCurrentSelectedSlot()
	{
		return _currentSelectedSlot;
	}

	/// <summary>
	/// Get the currently selected item
	/// </summary>
	public QuickSelectItem GetCurrentSelectedItem()
	{
		if (_currentSelectedSlot >= 0 && _currentSelectedSlot < SLOT_COUNT)
		{
			return ResourcesManager.Instance?.GetQuickSelectItem(_currentSelectedSlot);
		}
		return null;
	}

	public bool AssignToolToFirstEmptySlot(ToolType toolType)
	{
		// Check if tool is already assigned
		if (IsToolAlreadyAssigned(toolType))
		{
			GD.Print($"Tool {toolType} is already assigned to a slot");
			return false;
		}

		for (int i = 0; i < SLOT_COUNT; i++)
		{
			var item = ResourcesManager.Instance?.GetQuickSelectItem(i);
			if (item != null && item.IsEmpty)
			{
				ResourcesManager.Instance?.SetQuickSelectTool(i, toolType);
				RefreshSlot(i);

				// Show unassign elements if inventory is open
				ShowUnassignElementsForSlot(i);

				return true;
			}
		}
		return false;
	}

	public bool AssignResourceToFirstEmptySlot(ResourceType resourceType)
	{
		// Check if resource can be assigned to quick action panel
		if (!CanResourceBeAssigned(resourceType))
		{
			GD.Print($"Resource {resourceType} cannot be assigned to quick action panel");
			return false;
		}

		// Check if resource is already assigned
		if (IsResourceAlreadyAssigned(resourceType))
		{
			GD.Print($"Resource {resourceType} is already assigned to a slot");
			return false;
		}

		for (int i = 0; i < SLOT_COUNT; i++)
		{
			var item = ResourcesManager.Instance?.GetQuickSelectItem(i);
			if (item != null && item.IsEmpty)
			{
				ResourcesManager.Instance?.SetQuickSelectResource(i, resourceType);
				RefreshSlot(i);

				// Show unassign elements if inventory is open
				ShowUnassignElementsForSlot(i);

				return true;
			}
		}
		return false;
	}

	/// <summary>
	/// Handle unassign button click for a specific slot
	/// </summary>
	private void OnUnassignButtonClicked(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= SLOT_COUNT) return;

		var item = ResourcesManager.Instance?.GetQuickSelectItem(slotIndex);
		if (item == null || item.IsEmpty) return;

		// Clear the slot
		ResourcesManager.Instance?.ClearQuickSelectSlot(slotIndex);
		RefreshSlot(slotIndex);

		// Hide unassign elements for this slot
		_unassignSprites[slotIndex].Visible = false;
		_unassignButtons[slotIndex].Visible = false;

		GD.Print($"Unassigned item from slot {slotIndex + 1}");
	}

	/// <summary>
	/// Show unassign buttons and sprites for slots that have assigned items
	/// Called when inventory menu opens
	/// </summary>
	public void ShowUnassignElements()
	{
		for (int i = 0; i < SLOT_COUNT; i++)
		{
			var item = ResourcesManager.Instance?.GetQuickSelectItem(i);
			if (item != null && !item.IsEmpty)
			{
				_unassignSprites[i].Visible = true;
				_unassignButtons[i].Visible = true;
			}
		}
	}

	/// <summary>
	/// Hide all unassign buttons and sprites
	/// Called when inventory menu closes
	/// </summary>
	public void HideUnassignElements()
	{
		for (int i = 0; i < SLOT_COUNT; i++)
		{
			_unassignSprites[i].Visible = false;
			_unassignButtons[i].Visible = false;
		}

		// Refresh current selection to apply any changes made during inventory management
		RefreshCurrentSelection();
	}

	/// <summary>
	/// Show unassign elements for a specific slot if it has an assigned item
	/// Called when new items are assigned while inventory is open
	/// </summary>
	private void ShowUnassignElementsForSlot(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= SLOT_COUNT) return;

		// Only show if inventory is currently open (check if any unassign element is visible)
		bool inventoryIsOpen = false;
		for (int i = 0; i < SLOT_COUNT; i++)
		{
			if (_unassignSprites[i].Visible || _unassignButtons[i].Visible)
			{
				inventoryIsOpen = true;
				break;
			}
		}

		if (inventoryIsOpen)
		{
			var item = ResourcesManager.Instance?.GetQuickSelectItem(slotIndex);
			if (item != null && !item.IsEmpty)
			{
				_unassignSprites[slotIndex].Visible = true;
				_unassignButtons[slotIndex].Visible = true;
			}
		}
	}

	/// <summary>
	/// Check if a tool is already assigned to any slot
	/// </summary>
	private bool IsToolAlreadyAssigned(ToolType toolType)
	{
		for (int i = 0; i < SLOT_COUNT; i++)
		{
			var item = ResourcesManager.Instance?.GetQuickSelectItem(i);
			if (item != null && !item.IsEmpty && item.IsTool && item.ToolType == toolType)
			{
				return true;
			}
		}
		return false;
	}

	/// <summary>
	/// Check if a resource can be assigned to the quick action panel
	/// </summary>
	private bool CanResourceBeAssigned(ResourceType resourceType)
	{
		// Resources that cannot be assigned to quick action panel
		return resourceType switch
		{
			ResourceType.Wood => false,
			ResourceType.Stone => false,
			ResourceType.Stone2 => false,
			ResourceType.Plank => false,
			ResourceType.WoodenBeam => false,
			ResourceType.WoodenStick => false,
			ResourceType.Net => false,
			ResourceType.Leaf => false,
			// Ores and bars cannot be assigned
			ResourceType.CopperOre => false,
			ResourceType.IronOre => false,
			ResourceType.GoldOre => false,
			ResourceType.IndigosiumOre => false,
			ResourceType.MithrilOre => false,
			ResourceType.ErithrydiumOre => false,
			ResourceType.AdamantiteOre => false,
			ResourceType.UraniumOre => false,
			ResourceType.CopperBar => false,
			ResourceType.IronBar => false,
			ResourceType.GoldBar => false,
			ResourceType.IndigosiumBar => false,
			ResourceType.MithrilBar => false,
			ResourceType.ErithrydiumBar => false,
			ResourceType.AdamantiteBar => false,
			ResourceType.UraniumBar => false,
			ResourceType.CopperSheet => false,
			ResourceType.IronSheet => false,
			ResourceType.GoldSheet => false,
			ResourceType.IndigosiumSheet => false,
			ResourceType.MithrilSheet => false,
			ResourceType.ErithrydiumSheet => false,
			ResourceType.AdamantiteSheet => false,
			ResourceType.UraniumSheet => false,
			// Consumable resources that CAN be assigned
			ResourceType.Berry => true,
			ResourceType.RawRabbitLeg => true,
			ResourceType.CookedRabbitLeg => true,
			// Default: cannot be assigned
			_ => false
		};
	}

	/// <summary>
	/// Check if a resource is already assigned to any slot
	/// </summary>
	private bool IsResourceAlreadyAssigned(ResourceType resourceType)
	{
		for (int i = 0; i < SLOT_COUNT; i++)
		{
			var item = ResourcesManager.Instance?.GetQuickSelectItem(i);
			if (item != null && !item.IsEmpty && !item.IsTool && item.ResourceType == resourceType)
			{
				return true;
			}
		}
		return false;
	}

	/// <summary>
	/// Refresh the current selection to apply any changes made during inventory management
	/// Called when inventory closes to ensure PlayerController has the correct tool
	/// </summary>
	private void RefreshCurrentSelection()
	{
		if (_currentSelectedSlot >= 0 && _currentSelectedSlot < SLOT_COUNT)
		{
			var item = ResourcesManager.Instance?.GetQuickSelectItem(_currentSelectedSlot);
			if (item != null && !item.IsEmpty)
			{
				if (item.IsTool)
				{
					// Update PlayerController with the current tool in this slot
					var player = GetNode<PlayerController>("/root/world/Player");
					if (player != null)
					{
						player.SetCurrentTool(item.ToolType);
						GD.Print($"Refreshed selection: Updated PlayerController to tool {item.ToolType} in slot {_currentSelectedSlot + 1}");
					}
				}
				else
				{
					// Handle resource selection (future implementation)
					GD.Print($"Refreshed selection: Resource {item.ResourceType} in slot {_currentSelectedSlot + 1}");
				}
			}
			else
			{
				// Slot is now empty, clear tool selection
				var player = GetNode<PlayerController>("/root/world/Player");
				if (player != null)
				{
					player.SetCurrentTool(ToolType.None);
					GD.Print($"Refreshed selection: Slot {_currentSelectedSlot + 1} is now empty, cleared tool selection");
				}
			}
		}
	}

	public override void _ExitTree()
	{
		// Unsubscribe from events
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.ResourceChanged -= OnResourceChanged;
		}
	}
}
