1. Make fixes:
* when i open anvil menu (probably other too?) and then i open inventory menu then anvil menu is not closed
* when i have a berry in inventory, i select it then placeholder shows that its selected. then i click use it and it's used but placeholder showing selected item disappears - it should still be visible (only hide if amount = 0 and item disapears).

2. autosave.json - i added save file to tasks/autosave.json - when saved it takes a lot of space. can we somehow reduce it? maybe only save properties that differ from defaults for each tile? and then use for example gzip or some binary save format (if you decide to use it then in save handler make a private static bool flag that will indicate to use gzip or not so that i can test it without gzipping)? - just make sure that you don't break anytking!

3. I want to add 4 different mushrooms - Brown Mushroom, Blue Mushroom, Red Mushroom and Violet Mushroom - duplicate GreenBush and add those mushrooms scenes and cs. Also, add ResourceType for them (appropriate mushroom should be dropped so add icon and resource), add them in texture, add ItemInformation and translations. I will set texture manager textures and i will set textures of mushroom in scenes. Also, player can eat brown mushroom, blue mushroom and violet mushroom - each gives +5 food and +1 water.