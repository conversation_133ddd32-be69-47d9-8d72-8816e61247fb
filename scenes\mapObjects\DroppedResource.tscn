[gd_scene load_steps=4 format=3 uid="uid://dryrh52ahbyd2"]

[ext_resource type="Script" uid="uid://du8pm0o2gnfvf" path="res://scenes/mapObjects/DroppedResource.cs" id="1_dropped_resource"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="3_n8aia"]

[sub_resource type="CircleShape2D" id="CircleShape2D_collection"]
radius = 8.0

[node name="DroppedResource" type="Node2D"]
z_index = 1
y_sort_enabled = true
script = ExtResource("1_dropped_resource")

[node name="Sprite2D" type="Sprite2D" parent="."]

[node name="LabelAmount" parent="Sprite2D" instance=ExtResource("3_n8aia")]
offset_left = -25.0
offset_top = -5.0
offset_right = 9.0
offset_bottom = 14.0
scale = Vector2(0.5, 0.5)
text = ""
horizontal_alignment = 2

[node name="CollectionArea" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="CollectionArea"]
shape = SubResource("CircleShape2D_collection")
