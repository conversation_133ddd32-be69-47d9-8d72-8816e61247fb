I added Shop.tscn - it is a scene of the shop. When player enters it's range PlayerCollider (from the shop scene), player collistion to take into consideration is called PlayerDetector (verify in player script which layer does it use to detect player) then you need to open ShopMenu.tscn. Opening it is equal to calling it's "Open" animation (from AnimationPlayer located in ShopMenu.tscn). Same for closing but it's 'Close' animation when player clicks CloseButton. Initially to hide menu, you need to make it's "Control/Panel" node invisible. Read ShopMenu.tscn, Shop.tscn, Player.tscn (and player script) to understand how it works.
Your tasks are:
1. Handle open/close of shop panel
2. In shop panel - it's partially similar to InventoryMenu.tscn - read it and it's script to understand how it works. So in ShopMenu.tscn there is "panel" in which there are MenuSelectionSector-s (there are 5, initially player will only have 2 unlocked in his inventory then can unlock remaining 3 but here we already have 5). So you need to present inventory just like in player inventory panel - but only for resources (not tools). Then there is an InfoBoard which looks similar to that from InventoryMenu - but it has sell button instead of produce button. It also has SellPrice label - set it to amount that player will gain when he sell selected amount of items. In ResourceInformation add 2 fields: "SellPrice" - use it to calculate sell price. Also add "BuyPrice" - don't use it yet, but it should be 2x SellPrice. So when player selects amount of items and clicks sell - you should sell these items and add money to player.

Now, in ShopMenu (it's inside Panel, next to InfoBoard), wen you have 4 items (Item1, Item2, Item3, Item4) - these are items that player can buy. It's similar to selling - but in opposite direction. So you need to:
- display items that player can buy - that should be refreshed every day (when day in game passes). That should be random items that player can buy. Add in game save data (singleton) an array of items that player can buy - initially add there wood, stone, wooden plank and wooden stick (i will fill this later). We need to store item type and weight. weight will be used to calculate how much of each item player can buy (better items will have higher weight). So in this items to buy, when you randomly select them (that one that player can buy because they are unlocked), then select random number from 1 to 100 - then devide it by selected item weight - this will be amount of items that player can buy (if you get item value after comma eg 0,1 or 1,1 then round it to nearest integer up - so that we have 1 and 2 accordingly).
- display price of each item (use BuyPrice from ResourceInformation)
- display available amount of each item (use AvailableAmount from ResourceInformation)


3. Add translations