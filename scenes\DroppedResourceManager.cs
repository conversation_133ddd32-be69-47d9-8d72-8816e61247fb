using Godot;

public partial class DroppedResourceManager : Node
{
	private bool _hasLoadedResources = false;

	public override void _Ready()
	{
		// Subscribe to game loaded event to spawn saved dropped resources
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.GameLoaded += OnGameLoaded;
			
			// Also load immediately if ResourcesManager is already initialized
			CallDeferred(nameof(LoadDroppedResources));
		}
	}

	private void OnGameLoaded(object sender, System.EventArgs e)
	{
		LoadDroppedResources();
	}

	private void LoadDroppedResources()
	{
		if (_hasLoadedResources) return;

		var droppedResources = GameSaveData.Instance.WorldData.DroppedResources;
		if (droppedResources == null || droppedResources.Count == 0) return;

		GD.Print($"DroppedResourceManager: Loading {droppedResources.Count} dropped resources");

		foreach (var droppedResourceData in droppedResources)
		{
			SpawnDroppedResourceFromData(droppedResourceData);
		}

		_hasLoadedResources = true;
	}

	private void SpawnDroppedResourceFromData(DroppedResourceData data)
	{
		// Load the DroppedResource scene
		var droppedResourceScene = GD.Load<PackedScene>("res://scenes/mapObjects/DroppedResource.tscn");
		if (droppedResourceScene == null)
		{
			GD.PrintErr("DroppedResourceManager: Could not load DroppedResource.tscn");
			return;
		}

		// Create instance
		var droppedResource = droppedResourceScene.Instantiate<DroppedResource>();
		if (droppedResource == null)
		{
			GD.PrintErr("DroppedResourceManager: Failed to instantiate DroppedResource");
			return;
		}

		// Set properties from save data
		var position = new Vector2(data.X, data.Y);
		droppedResource.GlobalPosition = position;
		droppedResource.ResourceType = data.ResourceType;
		droppedResource.Quantity = data.Quantity;
		droppedResource.Id = data.Id;

		// Add to scene (find the main world scene)
		var worldScene = GetTree().CurrentScene;
		if (worldScene != null)
		{
			worldScene.AddChild(droppedResource);
		}
		else
		{
			// Fallback: add to this node's parent
			GetParent()?.AddChild(droppedResource);
		}

		// Note: We don't register with ResourcesManager again since it's already in save data
	}

	public override void _ExitTree()
	{
		// Unsubscribe from events
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.GameLoaded -= OnGameLoaded;
		}
	}
}
