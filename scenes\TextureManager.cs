using Godot;

public partial class TextureManager : Node
{
	// Singleton instance
	public static TextureManager Instance { get; private set; }

	// Resource textures (for UI, inventory, etc.) - set these in the editor
	[Export] public Texture2D WoodTexture { get; set; }
	[Export] public Texture2D StoneTexture { get; set; }
	[Export] public Texture2D NetTexture { get; set; }
	[Export] public Texture2D PlankTexture { get; set; }
	[Export] public Texture2D Stone2Texture { get; set; }
	[Export] public Texture2D BerryTexture { get; set; }
	[Export] public Texture2D LeafTexture { get; set; }
	[Export] public Texture2D BranchTexture { get; set; }
	[Export] public Texture2D CharcoalTexture { get; set; }

	[Export] public Texture2D CopperOreTexture { get; set; }
	[Export] public Texture2D IronOreTexture { get; set; }
	[Export] public Texture2D GoldOreTexture { get; set; }
	[Export] public Texture2D IndigosiumOreTexture { get; set; }
	[Export] public Texture2D MithrilOreTexture { get; set; }
	[Export] public Texture2D ErithrydiumOreTexture { get; set; }
	[Export] public Texture2D AdamantiteOreTexture { get; set; }
	[Export] public Texture2D UraniumOreTexture { get; set; }

	[Export] public Texture2D CopperBarTexture { get; set; }
	[Export] public Texture2D IronBarTexture { get; set; }
	[Export] public Texture2D GoldBarTexture { get; set; }
	[Export] public Texture2D IndigosiumBarTexture { get; set; }
	[Export] public Texture2D MithrilBarTexture { get; set; }
	[Export] public Texture2D ErithrydiumBarTexture { get; set; }
	[Export] public Texture2D AdamantiteBarTexture { get; set; }
	[Export] public Texture2D UraniumBarTexture { get; set; }

	[Export] public Texture2D CopperSheetTexture { get; set; }
	[Export] public Texture2D IronSheetTexture { get; set; }
	[Export] public Texture2D GoldSheetTexture { get; set; }
	[Export] public Texture2D IndigosiumSheetTexture { get; set; }
	[Export] public Texture2D MithrilSheetTexture { get; set; }
	[Export] public Texture2D ErithrydiumSheetTexture { get; set; }
	[Export] public Texture2D AdamantiteSheetTexture { get; set; }
	[Export] public Texture2D UraniumSheetTexture { get; set; }

	[Export] public Texture2D WoodenBeamTexture { get; set; }
	[Export] public Texture2D WoodenStickTexture { get; set; }
	[Export] public Texture2D RawRabbitLegTexture { get; set; }
	[Export] public Texture2D CookedRabbitLegTexture { get; set; }
	[Export] public Texture2D WoodenKeyTexture { get; set; }
	[Export] public Texture2D ArrowTexture { get; set; }
	[Export] public Texture2D CopperKeyTexture { get; set; }
	[Export] public Texture2D IronKeyTexture { get; set; }
	[Export] public Texture2D GoldKeyTexture { get; set; }
	[Export] public Texture2D IndigosiumKeyTexture { get; set; }
	[Export] public Texture2D MithrilKeyTexture { get; set; }
	[Export] public Texture2D ErithrydiumKeyTexture { get; set; }
	[Export] public Texture2D AdamantiteKeyTexture { get; set; }
	[Export] public Texture2D UraniumKeyTexture { get; set; }

	[Export] public Texture2D BrownMushroomTexture { get; set; }
	[Export] public Texture2D BlueMushroomTexture { get; set; }
	[Export] public Texture2D RedMushroomTexture { get; set; }
	[Export] public Texture2D VioletMushroomTexture { get; set; }

	// Resource icon textures (for dropped items, small displays) - set these in the editor
	[Export] public Texture2D WoodIconTexture { get; set; }
	[Export] public Texture2D StoneIconTexture { get; set; }
	[Export] public Texture2D NetIconTexture { get; set; }
	[Export] public Texture2D PlankIconTexture { get; set; }
	[Export] public Texture2D Stone2IconTexture { get; set; }
	[Export] public Texture2D BerryIconTexture { get; set; }
	[Export] public Texture2D LeafIconTexture { get; set; }
	[Export] public Texture2D BranchIconTexture { get; set; }
	[Export] public Texture2D CharcoalIconTexture { get; set; }

	[Export] public Texture2D BrownMushroomIconTexture { get; set; }
	[Export] public Texture2D BlueMushroomIconTexture { get; set; }
	[Export] public Texture2D RedMushroomIconTexture { get; set; }
	[Export] public Texture2D VioletMushroomIconTexture { get; set; }

	[Export] public Texture2D CopperOreIconTexture { get; set; }
	[Export] public Texture2D IronOreIconTexture { get; set; }
	[Export] public Texture2D GoldOreIconTexture { get; set; }
	[Export] public Texture2D IndigosiumOreIconTexture { get; set; }
	[Export] public Texture2D MithrilOreIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumOreIconTexture { get; set; }
	[Export] public Texture2D AdamantiteOreIconTexture { get; set; }
	[Export] public Texture2D UraniumOreIconTexture { get; set; }

	[Export] public Texture2D CopperBarIconTexture { get; set; }
	[Export] public Texture2D IronBarIconTexture { get; set; }
	[Export] public Texture2D GoldBarIconTexture { get; set; }
	[Export] public Texture2D IndigosiumBarIconTexture { get; set; }
	[Export] public Texture2D MithrilBarIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumBarIconTexture { get; set; }
	[Export] public Texture2D AdamantiteBarIconTexture { get; set; }
	[Export] public Texture2D UraniumBarIconTexture { get; set; }

	[Export] public Texture2D CopperSheetIconTexture { get; set; }
	[Export] public Texture2D IronSheetIconTexture { get; set; }
	[Export] public Texture2D GoldSheetIconTexture { get; set; }
	[Export] public Texture2D IndigosiumSheetIconTexture { get; set; }
	[Export] public Texture2D MithrilSheetIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumSheetIconTexture { get; set; }
	[Export] public Texture2D AdamantiteSheetIconTexture { get; set; }
	[Export] public Texture2D UraniumSheetIconTexture { get; set; }

	[Export] public Texture2D WoodenBeamIconTexture { get; set; }
	[Export] public Texture2D WoodenStickIconTexture { get; set; }
	[Export] public Texture2D RawRabbitLegIconTexture { get; set; }
	[Export] public Texture2D CookedRabbitLegIconTexture { get; set; }
	[Export] public Texture2D WoodenKeyIconTexture { get; set; }
	[Export] public Texture2D ArrowIconTexture { get; set; }
	[Export] public Texture2D CopperKeyIconTexture { get; set; }
	[Export] public Texture2D IronKeyIconTexture { get; set; }
	[Export] public Texture2D GoldKeyIconTexture { get; set; }
	[Export] public Texture2D IndigosiumKeyIconTexture { get; set; }
	[Export] public Texture2D MithrilKeyIconTexture { get; set; }
	[Export] public Texture2D ErithrydiumKeyIconTexture { get; set; }
	[Export] public Texture2D AdamantiteKeyIconTexture { get; set; }
	[Export] public Texture2D UraniumKeyIconTexture { get; set; }
	[Export] public Texture2D CoinIconTexture { get; set; }

	// Tool textures (for UI panels) - set these in the editor
	// Pickaxe variants
	[Export] public Texture2D PickaxeTexture { get; set; } // Default wooden pickaxe
	[Export] public Texture2D StonePickaxeTexture { get; set; }
	[Export] public Texture2D CopperPickaxeTexture { get; set; }
	[Export] public Texture2D IronPickaxeTexture { get; set; }
	[Export] public Texture2D GoldPickaxeTexture { get; set; }
	[Export] public Texture2D IndigosiumPickaxeTexture { get; set; }
	[Export] public Texture2D MithrilPickaxeTexture { get; set; }
	[Export] public Texture2D ErithrydiumPickaxeTexture { get; set; }
	[Export] public Texture2D AdamantitePickaxeTexture { get; set; }
	[Export] public Texture2D UraniumPickaxeTexture { get; set; }
	
	// Hammer variants
	[Export] public Texture2D HammerTexture { get; set; } // Default wooden hammer
	[Export] public Texture2D StoneHammerTexture { get; set; }
	[Export] public Texture2D CopperHammerTexture { get; set; }
	[Export] public Texture2D IronHammerTexture { get; set; }
	[Export] public Texture2D GoldHammerTexture { get; set; }
	[Export] public Texture2D IndigosiumHammerTexture { get; set; }
	[Export] public Texture2D MithrilHammerTexture { get; set; }
	[Export] public Texture2D ErithrydiumHammerTexture { get; set; }
	[Export] public Texture2D AdamantiteHammerTexture { get; set; }
	[Export] public Texture2D UraniumHammerTexture { get; set; }
	
	// Other tools
	[Export] public Texture2D WateringCanTexture { get; set; }
	[Export] public Texture2D HoeTexture { get; set; }
	[Export] public Texture2D SwordTexture { get; set; }
	[Export] public Texture2D BowTexture { get; set; }

	public override void _Ready()
	{
		// Set singleton instance
		if (Instance == null)
		{
			Instance = this;
			GD.Print("TextureManager initialized and ready");
		}
		else
		{
			// Prevent duplicate instances
			QueueFree();
		}
	}

	public Texture2D GetResourceTexture(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.Wood => WoodTexture,
			ResourceType.Stone => StoneTexture,
			ResourceType.Net => NetTexture,
			ResourceType.Plank => PlankTexture,
			ResourceType.Stone2 => Stone2Texture,
			ResourceType.Berry => BerryTexture,
			ResourceType.Leaf => LeafTexture,
			ResourceType.Branch => BranchTexture,
			ResourceType.Charcoal => CharcoalTexture,
			ResourceType.CopperOre => CopperOreTexture,
			ResourceType.IronOre => IronOreTexture,
			ResourceType.GoldOre => GoldOreTexture,
			ResourceType.IndigosiumOre => IndigosiumOreTexture,
			ResourceType.MithrilOre => MithrilOreTexture,
			ResourceType.ErithrydiumOre => ErithrydiumOreTexture,
			ResourceType.AdamantiteOre => AdamantiteOreTexture,
			ResourceType.UraniumOre => UraniumOreTexture,
			ResourceType.CopperBar => CopperBarTexture,
			ResourceType.IronBar => IronBarTexture,
			ResourceType.GoldBar => GoldBarTexture,
			ResourceType.IndigosiumBar => IndigosiumBarTexture,
			ResourceType.MithrilBar => MithrilBarTexture,
			ResourceType.ErithrydiumBar => ErithrydiumBarTexture,
			ResourceType.AdamantiteBar => AdamantiteBarTexture,
			ResourceType.UraniumBar => UraniumBarTexture,
			ResourceType.CopperSheet => CopperSheetTexture,
			ResourceType.IronSheet => IronSheetTexture,
			ResourceType.GoldSheet => GoldSheetTexture,
			ResourceType.IndigosiumSheet => IndigosiumSheetTexture,
			ResourceType.MithrilSheet => MithrilSheetTexture,
			ResourceType.ErithrydiumSheet => ErithrydiumSheetTexture,
			ResourceType.AdamantiteSheet => AdamantiteSheetTexture,
			ResourceType.UraniumSheet => UraniumSheetTexture,
			ResourceType.WoodenBeam => WoodenBeamTexture,
			ResourceType.WoodenStick => WoodenStickTexture,
			ResourceType.RawRabbitLeg => RawRabbitLegTexture,
			ResourceType.CookedRabbitLeg => CookedRabbitLegTexture,
			ResourceType.WoodenKey => WoodenKeyTexture,
			ResourceType.Arrow => ArrowTexture,
			ResourceType.CopperKey => CopperKeyTexture,
			ResourceType.IronKey => IronKeyTexture,
			ResourceType.GoldKey => GoldKeyTexture,
			ResourceType.IndigosiumKey => IndigosiumKeyTexture,
			ResourceType.MithrilKey => MithrilKeyTexture,
			ResourceType.ErithrydiumKey => ErithrydiumKeyTexture,
			ResourceType.AdamantiteKey => AdamantiteKeyTexture,
			ResourceType.UraniumKey => UraniumKeyTexture,
			ResourceType.BrownMushroom => BrownMushroomTexture,
			ResourceType.BlueMushroom => BlueMushroomTexture,
			ResourceType.RedMushroom => RedMushroomTexture,
			ResourceType.VioletMushroom => VioletMushroomTexture,
			_ => null
		};
	}

	public Texture2D GetResourceIconTexture(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.Wood => WoodIconTexture,
			ResourceType.Stone => StoneIconTexture,
			ResourceType.Net => NetIconTexture,
			ResourceType.Plank => PlankIconTexture,
			ResourceType.Stone2 => Stone2IconTexture,
			ResourceType.Berry => BerryIconTexture,
			ResourceType.Leaf => LeafIconTexture,
			ResourceType.Branch => BranchIconTexture,
			ResourceType.Charcoal => CharcoalIconTexture,
			ResourceType.CopperOre => CopperOreIconTexture,
			ResourceType.IronOre => IronOreIconTexture,
			ResourceType.GoldOre => GoldOreIconTexture,
			ResourceType.IndigosiumOre => IndigosiumOreIconTexture,
			ResourceType.MithrilOre => MithrilOreIconTexture,
			ResourceType.ErithrydiumOre => ErithrydiumOreIconTexture,
			ResourceType.AdamantiteOre => AdamantiteOreIconTexture,
			ResourceType.UraniumOre => UraniumOreIconTexture,
			ResourceType.CopperBar => CopperBarIconTexture,
			ResourceType.IronBar => IronBarIconTexture,
			ResourceType.GoldBar => GoldBarIconTexture,
			ResourceType.IndigosiumBar => IndigosiumBarIconTexture,
			ResourceType.MithrilBar => MithrilBarIconTexture,
			ResourceType.ErithrydiumBar => ErithrydiumBarIconTexture,
			ResourceType.AdamantiteBar => AdamantiteBarIconTexture,
			ResourceType.UraniumBar => UraniumBarIconTexture,
			ResourceType.CopperSheet => CopperSheetIconTexture,
			ResourceType.IronSheet => IronSheetIconTexture,
			ResourceType.GoldSheet => GoldSheetIconTexture,
			ResourceType.IndigosiumSheet => IndigosiumSheetIconTexture,
			ResourceType.MithrilSheet => MithrilSheetIconTexture,
			ResourceType.ErithrydiumSheet => ErithrydiumSheetIconTexture,
			ResourceType.AdamantiteSheet => AdamantiteSheetIconTexture,
			ResourceType.UraniumSheet => UraniumSheetIconTexture,
			ResourceType.WoodenBeam => WoodenBeamIconTexture,
			ResourceType.WoodenStick => WoodenStickIconTexture,
			ResourceType.RawRabbitLeg => RawRabbitLegIconTexture,
			ResourceType.CookedRabbitLeg => CookedRabbitLegIconTexture,
			ResourceType.WoodenKey => WoodenKeyIconTexture,
			ResourceType.Arrow => ArrowIconTexture,
			ResourceType.CopperKey => CopperKeyIconTexture,
			ResourceType.IronKey => IronKeyIconTexture,
			ResourceType.GoldKey => GoldKeyIconTexture,
			ResourceType.IndigosiumKey => IndigosiumKeyIconTexture,
			ResourceType.MithrilKey => MithrilKeyIconTexture,
			ResourceType.ErithrydiumKey => ErithrydiumKeyIconTexture,
			ResourceType.AdamantiteKey => AdamantiteKeyIconTexture,
			ResourceType.UraniumKey => UraniumKeyIconTexture,
			ResourceType.BrownMushroom => BrownMushroomIconTexture,
			ResourceType.BlueMushroom => BlueMushroomIconTexture,
			ResourceType.RedMushroom => RedMushroomIconTexture,
			ResourceType.VioletMushroom => VioletMushroomIconTexture,
			_ => null
		};
	}

	public bool HasTexture(ResourceType resourceType)
	{
		return GetResourceTexture(resourceType) != null;
	}

	public bool HasIconTexture(ResourceType resourceType)
	{
		return GetResourceIconTexture(resourceType) != null;
	}

	public Texture2D GetToolTexture(ToolType toolType, int level = 1)
	{
		if (toolType == ToolType.Pickaxe)
		{
			return level switch
			{
				1 => PickaxeTexture,
				2 => StonePickaxeTexture,
				3 => CopperPickaxeTexture,
				4 => IronPickaxeTexture,
				5 => GoldPickaxeTexture,
				6 => IndigosiumPickaxeTexture,
				7 => MithrilPickaxeTexture,
				8 => ErithrydiumPickaxeTexture,
				9 => AdamantitePickaxeTexture,
				10 => UraniumPickaxeTexture,
			};
		}
		else if (toolType == ToolType.Hammer)
		{
			return level switch
			{
				1 => HammerTexture,
				2 => StoneHammerTexture,
				3 => CopperHammerTexture,
				4 => IronHammerTexture,
				5 => GoldHammerTexture,
				6 => IndigosiumHammerTexture,
				7 => MithrilHammerTexture,
				8 => ErithrydiumHammerTexture,
				9 => AdamantiteHammerTexture,
				10 => UraniumHammerTexture,
			};
		}
		else
		{
			return toolType switch
			{
				ToolType.Hoe => HoeTexture,
				ToolType.WateringCan => WateringCanTexture,
				ToolType.Sword => SwordTexture,
				ToolType.Bow => BowTexture,
				_ => null
			};
		}
	}
}
