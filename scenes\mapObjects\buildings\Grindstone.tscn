[gd_scene load_steps=12 format=3 uid="uid://b4fxg8m50ebig"]

[ext_resource type="Texture2D" uid="uid://oj7o75fjlqre" path="res://resources/solaria/buildings/animated/Grindstone.png" id="1_grindstone"]
[ext_resource type="Script" uid="uid://tb2401kyq5u7" path="res://scenes/mapObjects/buildings/Grindstone.cs" id="1_script"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_dvujk"]
[ext_resource type="PackedScene" uid="uid://kvdvkjpx80mm" path="res://scenes/UI/buildingMenus/GrindstoneMenu.tscn" id="3_gptgy"]
[ext_resource type="PackedScene" uid="uid://b8xf7h2lam3pq" path="res://scenes/UI/progress/ProgressBarVertical.tscn" id="5_kcax7"]

[sub_resource type="Animation" id="Animation_hit"]
resource_name = "hit"
length = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Grindstone:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 0.42, 0.27, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_idle"]
resource_name = "idle"
length = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Grindstone:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="Animation" id="Animation_working"]
resource_name = "working"
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Grindstone:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.25, 0.5, 0.75),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_grindstone"]
_data = {
&"hit": SubResource("Animation_hit"),
&"idle": SubResource("Animation_idle"),
&"working": SubResource("Animation_working")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_grindstone"]
size = Vector2(14, 10)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_detector"]
size = Vector2(14, 18)

[node name="Grindstone" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_grindstone")
}
speed_scale = 0.6

[node name="Grindstone" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("1_grindstone")
hframes = 4

[node name="CraftingResource" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -5)
scale = Vector2(0.6, 0.75)

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 9)
shape = SubResource("RectangleShape2D_grindstone")

[node name="ProgressBar" parent="." instance=ExtResource("3_dvujk")]
position = Vector2(0, 17)
scale = Vector2(1, 0.6)

[node name="ProgressBarVertical" parent="." instance=ExtResource("5_kcax7")]
position = Vector2(10, 6)
scale = Vector2(0.8, 0.8)

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(0, 6)
shape = SubResource("RectangleShape2D_detector")

[node name="GrindstoneMenu" parent="." instance=ExtResource("3_gptgy")]
