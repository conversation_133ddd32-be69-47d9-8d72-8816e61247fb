1. grindstone is 16 px width 32 height. it's center is in the middle of an object. When placed it should take 2 tiles: 1x2 and be rendered in center. If hit with pickaxe it should be damaged just like other buildings - and hp bar called "ProgressBar" should be updated accordingly. Also when hit - it should animate like other buildings, see for example Anvil.
2. When in GrindstoneMenu i select for example plank, and I don't have resources to build them but i click for example 25% then i can craft 1 plank. Fix it so that it's not possible to select amount that you can't afford. Also, verify if the same logic works in CampfireMenu, Furnace1Mnue, Furnace2Menu, Furnace3Menu, Furnace4Menu.
3. How are mushrooms spawned? Make sure that if they are spawned then they are spawned in center of a tile. - they are 1x1 (16x16px) and center of them is in the center of a tile. When damaged - ProgressBar (health bar) should be refreshed accordingly or of destroyed, it should drop 1 mushroom of given type.
4. Create how_to_implement_