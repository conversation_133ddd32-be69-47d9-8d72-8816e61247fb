using Godot;

public partial class QuestBoard : Node2D
{
	private Area2D _playerDetectionArea;
	private QuestsMenu _questsMenu;
	private bool _isPlayerInRange = false;

	public override void _Ready()
	{
		_playerDetectionArea = GetNode<Area2D>("Area2D");
		_questsMenu = GetNode<QuestsMenu>("QuestsMenu");
		
		if (_playerDetectionArea != null)
		{
			GD.Print(11111);
			// Configure collision layers to detect player
			// Set collision mask to detect PlayerDetector (layer 3)
			_playerDetectionArea.CollisionMask = 4; // Detect layer 3 (bit 2 = 4)

			_playerDetectionArea.AreaEntered += OnPlayerEntered;
			_playerDetectionArea.AreaExited += OnPlayerExited;
		}
		else
		{
			GD.PrintErr("QuestBoard: Area2D not found!");
		}

		if (_questsMenu == null)
		{
			GD.PrintErr("QuestBoard: QuestsMenu not found!");
		}

		QuestManager.ActivateInitialQuests();
	}

	public override void _ExitTree()
	{
		if (_playerDetectionArea != null)
		{
			_playerDetectionArea.AreaEntered -= OnPlayerEntered;
			_playerDetectionArea.AreaExited -= OnPlayerExited;
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				ToggleQuestsMenu();
			}
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		GD.Print(area.Name);
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			GD.Print("QuestBoard: Player in range - press 'R' to open quests");
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
			GD.Print("QuestBoard: Player left range");
		}
	}

	private void ToggleQuestsMenu()
	{
		if (_questsMenu != null)
		{
			_questsMenu.OpenMenu();
		}
		else
		{
			GD.PrintErr("QuestBoard: QuestsMenu reference not set!");
		}
	}
}
