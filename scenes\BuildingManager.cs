using Godot;
using System.Collections.Generic;

public partial class BuildingManager : Node2D
{
	[Export] public PackedScene AnvilScene { get; set; }
	[Export] public PackedScene BridgeScene { get; set; }
	[Export] public PackedScene CampfireScene { get; set; }
	[Export] public PackedScene Furnace1Scene { get; set; }
	[Export] public PackedScene Furnace2Scene { get; set; }
	[Export] public PackedScene Furnace3Scene { get; set; }
	[Export] public PackedScene Furnace4Scene { get; set; }
	[Export] public PackedScene GrindstoneScene { get; set; }

	private Dictionary<string, Node2D> _activeBuildings = new();

	public override void _Ready()
	{
		if (AnvilScene == null)
		{
			AnvilScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Anvil.tscn");
		}

		if (BridgeScene == null)
		{
			BridgeScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Bridge.tscn");
		}

		if (CampfireScene == null)
		{
			CampfireScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Campfire.tscn");
		}

		if (Furnace1Scene == null)
		{
			Furnace1Scene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Furnace1.tscn");
		}

		if (Furnace2Scene == null)
		{
			Furnace2Scene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Furnace2.tscn");
		}

		if (Furnace3Scene == null)
		{
			Furnace3Scene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Furnace3.tscn");
		}

		if (Furnace4Scene == null)
		{
			Furnace4Scene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Furnace4.tscn");
		}

		if (GrindstoneScene == null)
		{
			GrindstoneScene = GD.Load<PackedScene>("res://scenes/mapObjects/buildings/Grindstone.tscn");
		}

		CallDeferred(nameof(LoadExistingBuildings));
	}

	private void LoadExistingBuildings()
	{
		var buildings = ResourcesManager.Instance?.GetBuildings();
		if (buildings == null || buildings.Count == 0)
		{
			return;
		}

		foreach (var buildingData in buildings)
		{
			LoadBuilding(buildingData);
		}
	}

	private bool LoadBuilding(BuildingData buildingData)
	{
		if (string.IsNullOrEmpty(buildingData.BuildingType))
		{
			return false;
		}

		if (_activeBuildings.ContainsKey(buildingData.Id))
		{
			return false;
		}

		Node2D buildingInstance = null;

		switch (buildingData.BuildingType)
		{
			case "Anvil":
				buildingInstance = LoadAnvil(buildingData);
				break;
			case "Bridge":
				buildingInstance = LoadBridge(buildingData);
				break;
			case "Campfire":
				buildingInstance = LoadCampfire(buildingData);
				break;
			case "Furnace1":
				buildingInstance = LoadFurnace1(buildingData);
				break;
			case "Furnace2":
				buildingInstance = LoadFurnace2(buildingData);
				break;
			case "Furnace3":
				buildingInstance = LoadFurnace3(buildingData);
				break;
			case "Furnace4":
				buildingInstance = LoadFurnace4(buildingData);
				break;
			case "Grindstone":
				buildingInstance = LoadGrindstone(buildingData);
				break;
			default:
				return false;
		}

		if (buildingInstance != null)
		{
			_activeBuildings[buildingData.Id] = buildingInstance;
			return true;
		}

		return false;
	}

	private Anvil LoadAnvil(BuildingData buildingData)
	{
		if (AnvilScene == null)
		{
			return null;
		}

		var anvil = AnvilScene.Instantiate<Anvil>();
		if (anvil == null)
		{
			return null;
		}

		anvil.LoadFromSaveData(buildingData);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", anvil);
		}
		else
		{
			GetParent().CallDeferred("add_child", anvil);
		}

		return anvil;
	}

	private Bridge LoadBridge(BuildingData buildingData)
	{
		if (BridgeScene == null)
		{
			return null;
		}

		var bridge = BridgeScene.Instantiate<Bridge>();
		if (bridge == null)
		{
			return null;
		}

		bridge.LoadFromSaveData(buildingData);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", bridge);
		}
		else
		{
			GetParent().CallDeferred("add_child", bridge);
		}

		return bridge;
	}

	private Campfire LoadCampfire(BuildingData buildingData)
	{
		if (CampfireScene == null)
		{
			return null;
		}

		var campfire = CampfireScene.Instantiate<Campfire>();
		if (campfire == null)
		{
			return null;
		}

		campfire.LoadFromSaveData(buildingData);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", campfire);
		}
		else
		{
			GetParent().CallDeferred("add_child", campfire);
		}

		return campfire;
	}

	private Furnace1 LoadFurnace1(BuildingData buildingData)
	{
		if (Furnace1Scene == null)
		{
			return null;
		}

		var furnace = Furnace1Scene.Instantiate<Furnace1>();
		if (furnace == null)
		{
			return null;
		}

		furnace.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		furnace.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", furnace);
		}
		else
		{
			GetParent().CallDeferred("add_child", furnace);
		}

		return furnace;
	}

	private Furnace2 LoadFurnace2(BuildingData buildingData)
	{
		if (Furnace2Scene == null)
		{
			return null;
		}

		var furnace = Furnace2Scene.Instantiate<Furnace2>();
		if (furnace == null)
		{
			return null;
		}

		furnace.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		furnace.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", furnace);
		}
		else
		{
			GetParent().CallDeferred("add_child", furnace);
		}

		return furnace;
	}

	private Furnace3 LoadFurnace3(BuildingData buildingData)
	{
		if (Furnace3Scene == null)
		{
			return null;
		}

		var furnace = Furnace3Scene.Instantiate<Furnace3>();
		if (furnace == null)
		{
			return null;
		}

		furnace.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		furnace.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", furnace);
		}
		else
		{
			GetParent().CallDeferred("add_child", furnace);
		}

		return furnace;
	}

	private Furnace4 LoadFurnace4(BuildingData buildingData)
	{
		if (Furnace4Scene == null)
		{
			return null;
		}

		var furnace = Furnace4Scene.Instantiate<Furnace4>();
		if (furnace == null)
		{
			return null;
		}

		furnace.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		furnace.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", furnace);
		}
		else
		{
			GetParent().CallDeferred("add_child", furnace);
		}

		return furnace;
	}

	private Grindstone LoadGrindstone(BuildingData buildingData)
	{
		if (GrindstoneScene == null)
		{
			return null;
		}

		var grindstone = GrindstoneScene.Instantiate<Grindstone>();
		if (grindstone == null)
		{
			return null;
		}

		grindstone.LoadFromSave(new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY),
							GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager"),
							buildingData.CurrentHealth);
		grindstone.SetSaveId(buildingData.Id);

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.CallDeferred("add_child", grindstone);
		}
		else
		{
			GetParent().CallDeferred("add_child", grindstone);
		}

		return grindstone;
	}

	public bool RemoveBuilding(string buildingId)
	{
		if (_activeBuildings.TryGetValue(buildingId, out var building))
		{
			building.QueueFree();
			_activeBuildings.Remove(buildingId);
			return true;
		}
		return false;
	}

	public Dictionary<string, Node2D> GetAllBuildings()
	{
		return new Dictionary<string, Node2D>(_activeBuildings);
	}

	public void ClearAllBuildings()
	{
		foreach (var building in _activeBuildings.Values)
		{
			building.QueueFree();
		}
		_activeBuildings.Clear();

		ResourcesManager.Instance?.ClearBuildings();
	}
}
