using Godot;
using System;
using System.Text.Json;
using System.Text.Json.Serialization;

/// <summary>
/// Custom JSON converter for Vector2I that supports dictionary key serialization
/// </summary>
public class Vector2IJsonConverter : JsonConverter<Vector2I>
{
    public override Vector2I Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            // Handle dictionary key format: "x,y"
            var value = reader.GetString();
            if (TryParseVector2I(value, out var vector))
            {
                return vector;
            }
            throw new JsonException($"Unable to parse Vector2I from string: {value}");
        }
        
        if (reader.TokenType == JsonTokenType.StartObject)
        {
            // Handle object format: {"x": 1, "y": 2}
            int x = 0, y = 0;
            
            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    return new Vector2I(x, y);
                }
                
                if (reader.TokenType == JsonTokenType.PropertyName)
                {
                    var propertyName = reader.GetString()?.ToLowerInvariant();
                    reader.Read();
                    
                    switch (propertyName)
                    {
                        case "x":
                            x = reader.GetInt32();
                            break;
                        case "y":
                            y = reader.GetInt32();
                            break;
                    }
                }
            }
        }
        
        throw new JsonException("Unable to parse Vector2I");
    }

    public override void Write(Utf8JsonWriter writer, Vector2I value, JsonSerializerOptions options)
    {
        writer.WriteStartObject();
        writer.WriteNumber("x", value.X);
        writer.WriteNumber("y", value.Y);
        writer.WriteEndObject();
    }

    public override Vector2I ReadAsPropertyName(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var value = reader.GetString();
        if (TryParseVector2I(value, out var vector))
        {
            return vector;
        }
        throw new JsonException($"Unable to parse Vector2I property name: {value}");
    }

    public override void WriteAsPropertyName(Utf8JsonWriter writer, Vector2I value, JsonSerializerOptions options)
    {
        writer.WritePropertyName($"{value.X},{value.Y}");
    }

    private static bool TryParseVector2I(string value, out Vector2I vector)
    {
        vector = default;
        
        if (string.IsNullOrEmpty(value))
            return false;
            
        var parts = value.Split(',');
        if (parts.Length != 2)
            return false;
            
        if (int.TryParse(parts[0], out int x) && int.TryParse(parts[1], out int y))
        {
            vector = new Vector2I(x, y);
            return true;
        }
        
        return false;
    }
}
