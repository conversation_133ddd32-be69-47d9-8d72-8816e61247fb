1. in world.tscn there is SelectedToolPanel node and inside it MapButton node (i created MapButton.tscn) - in this scene there is Button. when i click button i want to open MapPanel (map panel is a scene that i created and added to world.tscn - it's MapPanel.tscn). It has AnimationPlayer, when you play Open animation - it shows panel and when you play Close - it hides panel. So when player clicks this button in MapButton i want to open panel and when player clicks CloseButton in MapPanel (find its location) i want to close panel. I also created a scene RegionUnlockButton which i added multiple times to MapPanel. You need to implement RegionUnlockButton.tscn to work like this: export 2 values: retion id and unlock price. player unlocks region one by one. see RegionUnlockManager which is assigned to world.tscn - you might need to adjust it's logic too. ok, so this RegionUnlockButton has Water sprite and Button sprite. when last region id unlocked is for example 5 and given button is for region id 7 then we should show water sprite and hide button sprite. when last region id unlocked is for example 5 and given button is for region id 6 then we should hide water sprite and show button sprite. and when region id is for example 5 and given button is for region id 5 or less, then we should queue free given button. Also, when player clicks to unlock region by pressing this button (UnlockButton node) then we should check if player has enough gold. if yes, then we should unlock region (see RegionUnlockManager) and subtract gold from player. if not enough gold then show some debug info. i will set region id and gold amount for each button in map scene. when region is unlocked then we should show it the same way like currently region id 2 and 3 are shown after unlocking.