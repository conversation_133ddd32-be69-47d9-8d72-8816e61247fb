# Task 1: NPC Dialog Flow
look at TutorialNPC.cs. I want the following flow of this npc:
1) when player comes to npc for the first time - show Welcome stage. When player comes to npc but he didn't built and anvil yet then you need to show Welcome2 state.
2) when anvil is built and player comes to npc again then npc should tell BuildAnvil texts
3) when player comes to npc and bridge is not yet built then npc should tell "Build the Bridge and return to me!" - we need additional state for this
4) when player build bridge and comes to npc then npc should tell BuildBridge texts
5) then when player comes again and he didnt hunt a rabbit yet then npc should tell "Come back once you've successfully hunted a rabbit!" - we need additional state for this
6) when player hunted rabbit and comes to npc then npc should tell HuntRabbit texts
7) when player comes again and he didnt build a campfire yet then npc should tell "Build a Campfire and return to me!" - we need additional state for this
8) when player build campfire and comes to npc then npc should tell BuildCampfire texts
9) when player comes again and he didnt cook a rabbit yet then npc should tell "Cook the Raw Rabbit Leg and return to me!" - we need additional state for this
10) when player cooked rabbit and comes to npc then npc should tell Complete texts
11) when complete texts are told then you should invoke this:
SpawnCompletionReward();
HideNPC(); but currently this is invoked before player even talks to npc so this texts from complete will never be shown.

# Task 2: translations
add translations for all texts in TutorialNPC.cs in following way: when we have for example stage Welcome then we should have translations for texts like NPC_TUTORIAL_WELCOME_1, NPC_TUTORIAL_WELCOME_2 etc for all steps and stages. Translations should be added to translations.csv

# Task 3: Refactor of camfire signal to player
In Campfire.cs we have following code:
// Notify tutorial NPC if cooked rabbit leg
		if (_selectedCraftingResource == ResourceType.CookedRabbitLeg)
		{
			var tutorialNpc = GetNode<TutorialNPC>("/root/world/TutorialNpc");
			if (tutorialNpc != null)
			{
				tutorialNpc.OnMeatCooked();
			}

			// Emit signal for region unlocking
			CommonSignals.Instance?.EmitRabbitLegCooked();
		}
i want to decouple tutorial npc from the campfire - so instead of calling tutorial npc directly from campfire we should emit signal from campfire when rabbit leg is cooked and listen to it in tutorial npc.