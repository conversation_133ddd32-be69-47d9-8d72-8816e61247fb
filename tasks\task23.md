1. i have warning fix: 
  <C++ Source>  scene/resources/resource_format_text.cpp:447 @ load()
W 0:00:01:457   load: res://scenes/mapObjects/buildings/Grindstone.tscn:3 - ext_resource, invalid UID: uid://grindstone_texture - using text path instead: res://resources/solaria/buildings/animated/Grindstone.png
  <C++ Source>  scene/resources/resource_format_text.cpp:447 @ load()
W 0:00:01:458   load: res://scenes/mapObjects/buildings/Grindstone.tscn:4 - ext_resource, invalid UID: uid://grindstone_script - using text path instead: res://scenes/mapObjects/buildings/Grindstone.cs
  <C++ Source>  scene/resources/resource_format_text.cpp:447 @ load()
W 0:00:01:465   load: res://scenes/UI/buildingMenus/GrindstoneMenu.tscn:3 - ext_resource, invalid UID: uid://grindstone_menu_script - using text path instead: res://scenes/UI/buildingMenus/GrindstoneMenu.cs
  <C++ Source>  scene/resources/resource_format_text.cpp:447 @ load()
2. I clicked to build grindstone but i didnt selecte place to build it and it was automatically build and placed on some (random?) place of the map. also, grindstone should take 1x2 space. (its 16x32).
3. I go to grindstone and click R button but menu is not opening. GrindstoneMenu was supposed to be a COPY of CampfireMenu but it has some poor ui. Remove this tscn and recreate it by copying CampfireMenu.tscn and adjust it to grindstone. Behaviour should be same as campfire, only different items.