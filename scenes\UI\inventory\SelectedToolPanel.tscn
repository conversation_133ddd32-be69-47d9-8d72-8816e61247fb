[gd_scene load_steps=36 format=3 uid="uid://c13q3mm3n1etg"]

[ext_resource type="Texture2D" uid="uid://cq17wxff6qqnb" path="res://resources/solaria/UI/selectToolPanel/selectToolPanel.png" id="1_bharj"]
[ext_resource type="Script" uid="uid://bw4u7cn4pryrn" path="res://scenes/UI/inventory/SelectedToolPanel.cs" id="1_selected_tool_panel"]
[ext_resource type="Texture2D" uid="uid://d18xs4d1jdnta" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected1.png" id="2_r7sf3"]
[ext_resource type="Texture2D" uid="uid://44arr3rjdn8u" path="res://resources/solaria/resources/tools/tool_pickaxe.png" id="3_mfwm0"]
[ext_resource type="Texture2D" uid="uid://ylaofseri7b4" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected2.png" id="4_2d738"]
[ext_resource type="Texture2D" uid="uid://shm60004rrg1" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected3.png" id="5_5bhj6"]
[ext_resource type="Texture2D" uid="uid://1sov5jmukq2s" path="res://resources/solaria/resources/tools/tool_hammer.png" id="5_h20gy"]
[ext_resource type="Texture2D" uid="uid://cewvneql4dscp" path="res://resources/solaria/UI/close_button_2.png" id="5_t2kjh"]
[ext_resource type="Texture2D" uid="uid://4xa0lgqymy1q" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected4.png" id="6_rau2o"]
[ext_resource type="Texture2D" uid="uid://do20y7dl6sfme" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected5.png" id="7_mfwm0"]
[ext_resource type="Texture2D" uid="uid://cc33cyn0b0vo3" path="res://resources/solaria/resources/tools/tool_shovel.png" id="7_n6ogd"]
[ext_resource type="Texture2D" uid="uid://051br3uv8tuv" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected6.png" id="8_h20gy"]
[ext_resource type="Texture2D" uid="uid://djypab2tb4ijn" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected7.png" id="9_n6ogd"]
[ext_resource type="Texture2D" uid="uid://dm2d8rdrplalw" path="res://resources/solaria/resources/tools/tool_watering_can.png" id="9_s2keg"]
[ext_resource type="Texture2D" uid="uid://cvcmbekoa0bkv" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected8.png" id="10_s2keg"]
[ext_resource type="Texture2D" uid="uid://c138dulkhwq0b" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected9.png" id="11_f630j"]
[ext_resource type="Texture2D" uid="uid://crl8txue1grse" path="res://resources/solaria/resources/tools/tool_net.png" id="11_rai3g"]
[ext_resource type="Texture2D" uid="uid://3gix1ionayu0" path="res://resources/solaria/UI/selectToolPanel/selectToolSelected0.png" id="12_rai3g"]
[ext_resource type="Texture2D" uid="uid://fsyy4wdm5s3p" path="res://resources/solaria/resources/tools/tool_hoe.png" id="13_4d0wc"]
[ext_resource type="PackedScene" uid="uid://mtskeuj17t5f" path="res://scenes/UI/build/BuildButton.tscn" id="20_cru81"]
[ext_resource type="PackedScene" uid="uid://b2tiwarnc1xbr" path="res://scenes/UI/mapPanel/MapButton.tscn" id="21_p3g1r"]

[sub_resource type="Animation" id="Animation_lspgn"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SelectedPlaceholder:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SelectedPlaceholder:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("ResourceIcon:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}

[sub_resource type="Animation" id="Animation_r7sf3"]
resource_name = "select"
length = 0.2
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SelectedPlaceholder:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SelectedPlaceholder:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("ResourceIcon:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_2d738"]
_data = {
&"RESET": SubResource("Animation_lspgn"),
&"select": SubResource("Animation_r7sf3")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5bhj6"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rau2o"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mfwm0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_h20gy"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_n6ogd"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_s2keg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_f630j"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rai3g"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4d0wc"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4xk7w"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_u15rx"]

[node name="SelectedToolPanel" type="CanvasLayer"]
script = ExtResource("1_selected_tool_panel")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -20.0
offset_top = -40.0
offset_right = 20.0
grow_horizontal = 2
grow_vertical = 0

[node name="Panel" type="Sprite2D" parent="Control"]
position = Vector2(0, 24)
texture = ExtResource("1_bharj")

[node name="Item1" type="Sprite2D" parent="Control/Panel"]
position = Vector2(0, -1)

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item1"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item1"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("2_r7sf3")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item1"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("3_mfwm0")

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item1"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="Button" type="Button" parent="Control/Panel/Item1"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="UnassignButton" type="Button" parent="Control/Panel/Item1"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="Item2" type="Sprite2D" parent="Control/Panel"]
position = Vector2(22, -1)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item2"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("4_2d738")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item2"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_h20gy")

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item2"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item2"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="Button" type="Button" parent="Control/Panel/Item2"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="UnassignButton" type="Button" parent="Control/Panel/Item2"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="Item3" type="Sprite2D" parent="Control/Panel"]
position = Vector2(44, -1)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item3"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_5bhj6")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item3"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("7_n6ogd")

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item3"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="Button" type="Button" parent="Control/Panel/Item3"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item3"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="UnassignButton" type="Button" parent="Control/Panel/Item3"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="Item4" type="Sprite2D" parent="Control/Panel"]
position = Vector2(66, -1)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item4"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("6_rau2o")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item4"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("9_s2keg")

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item4"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="Button" type="Button" parent="Control/Panel/Item4"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item4"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="UnassignButton" type="Button" parent="Control/Panel/Item4"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="Item5" type="Sprite2D" parent="Control/Panel"]
position = Vector2(88, -1)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item5"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("7_mfwm0")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item5"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("11_rai3g")

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item5"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="Button" type="Button" parent="Control/Panel/Item5"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item5"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="UnassignButton" type="Button" parent="Control/Panel/Item5"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="Item6" type="Sprite2D" parent="Control/Panel"]
position = Vector2(110, -1)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item6"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("8_h20gy")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item6"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("13_4d0wc")

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item6"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="Button" type="Button" parent="Control/Panel/Item6"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item6"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="UnassignButton" type="Button" parent="Control/Panel/Item6"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="Item7" type="Sprite2D" parent="Control/Panel"]
position = Vector2(132, -1)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item7"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("9_n6ogd")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item7"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item7"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="Button" type="Button" parent="Control/Panel/Item7"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item7"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="UnassignButton" type="Button" parent="Control/Panel/Item7"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="Item8" type="Sprite2D" parent="Control/Panel"]
position = Vector2(154, -1)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item8"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("10_s2keg")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item8"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item8"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="Button" type="Button" parent="Control/Panel/Item8"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item8"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="UnassignButton" type="Button" parent="Control/Panel/Item8"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="Item9" type="Sprite2D" parent="Control/Panel"]
position = Vector2(176, -1)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item9"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("11_f630j")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item9"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item9"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="Button" type="Button" parent="Control/Panel/Item9"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item9"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="UnassignButton" type="Button" parent="Control/Panel/Item9"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="Item0" type="Sprite2D" parent="Control/Panel"]
position = Vector2(198, -1)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Control/Panel/Item0"]
position = Vector2(-99, 0)
scale = Vector2(0.95, 0.95)
texture = ExtResource("12_rai3g")

[node name="ResourceIcon" type="Sprite2D" parent="Control/Panel/Item0"]
position = Vector2(-97, 0)
scale = Vector2(0.95, 0.95)

[node name="UnassignSprite" type="Sprite2D" parent="Control/Panel/Item0"]
position = Vector2(-98, -22)
scale = Vector2(0.95, 0.95)
texture = ExtResource("5_t2kjh")

[node name="Button" type="Button" parent="Control/Panel/Item0"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -109.0
offset_top = -12.0
offset_right = -89.0
offset_bottom = 11.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/Panel/Item0"]
libraries = {
&"": SubResource("AnimationLibrary_2d738")
}

[node name="UnassignButton" type="Button" parent="Control/Panel/Item0"]
anchors_preset = 9
anchor_bottom = 1.0
offset_left = -105.0
offset_top = -28.0
offset_right = -91.0
offset_bottom = -15.0
grow_vertical = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_5bhj6")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_rau2o")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mfwm0")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h20gy")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_n6ogd")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_s2keg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_f630j")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rai3g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_4d0wc")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_4xk7w")
theme_override_styles/normal = SubResource("StyleBoxEmpty_u15rx")

[node name="BuildButton" parent="Control" instance=ExtResource("20_cru81")]
position = Vector2(-131, 18)

[node name="MapButton" parent="Control" instance=ExtResource("21_p3g1r")]
position = Vector2(-163, 18)

[node name="WorldMapButton" type="Sprite2D" parent="Control"]
position = Vector2(-940, -1040)
