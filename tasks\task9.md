1. in player controller i have code for berries:
float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _lastBerryUseTime < _berryCooldown)
			{
				return;
			}

			_lastBerryUseTime = currentTime;
i want to have a timeout also for rabbit leg (both types). Also, this timeout needs to be reduced to 0.5s
2. when i place a campfire - it should take 2 places (tiles) but in vertical direction. so look at anvil - when built it takes 2 places horisontally. similar logic should be for campfire when placing/removing it but vertically.
3. Let's add Furnace1 (we will have 4 different furnaces). So i duplicated scene Anvil and created Furnace1, also i duplicated AnvilMenu.tscn to create Furnace1Menu.tscn. I have removed anvil scripts from them and changed names. Now, i want you to implement Furnace1 similar to Anvil. Difference is that Furnace1 takes 2x2 places (tiles) when placed - adjust logic accordingly. Also, furnace should animate only when it produces something, when not producing then should "RESET" animation. See structure of Furnace1.tscn to know how it looks. So Furnace1Menu will have 1 item to craft right now - stone bar. ItemList is ItemListStoneBar. Do similar like AnvilMenu. Add required resource - StoneBar (resource and icon) to TextureManager - i will set it later, also add ResourceType. Translate texts from Anvil1Menu (from labels) that are not yet translated - in translations csv. I want to be able to build this Furnace1 so in BuildMenu scene I added ItemListFurnace1 - handle it similar to other buildings.
4. Think/verify if your changes are ok - refactor if needed.
5. update technical.md
6. build the project